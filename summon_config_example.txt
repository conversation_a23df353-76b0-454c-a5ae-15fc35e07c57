{
  // 召唤配置文件说明
  // 每个技能可以配置多个召唤物，每个召唤物必须指定独立的数量和时长

  "skill2008": {
    "monsters": [
      // 格式: { "id": 召唤物ID, "count": 数量, "duration": 持续时间(秒) }
      { "id": 7, "count": 2, "duration": 1800 },      // 召唤物ID=7, 数量=2, 持续30分钟
      { "id": 576, "count": 3, "duration": 3600 },    // 召唤物ID=576, 数量=3, 持续1小时
      { "id": 577, "count": 1, "duration": 7200 }     // 召唤物ID=577, 数量=1, 持续2小时
    ]
  },

  "skill2009": {
    "monsters": [
      { "id": 7, "count": 1, "duration": 1800 },      // 30分钟
      { "id": 577, "count": 2, "duration": 3600 }     // 1小时
    ]
  },

  "skill2010": {
    "monsters": [
      { "id": 7, "count": 1, "duration": 5400 }       // 1.5小时
    ]
  },

  "skill2385": {
    "monsters": [
      { "id": 7, "count": 1, "duration": 7200 }       // 2小时
    ]
  }

  // 时间换算参考:
  // 1800秒 = 30分钟
  // 3600秒 = 1小时
  // 5400秒 = 1.5小时
  // 7200秒 = 2小时
  // 10800秒 = 3小时

  // 注意: 每个召唤物都必须指定 duration，不再有默认时长
}
