{
  // 召唤配置文件说明
  // 每个技能可以配置多个召唤物，每个召唤物必须指定独立的数量和时长

  "skill2008": {
    "monsters": [
      // 格式: { "id": 召唤物ID, "count": 数量, "duration": 持续时间(秒), "name": "自定义名称" }
      { "id": 7, "count": 2, "duration": 1800, "name": "小兵甲" },      // 召唤物ID=7, 数量=2, 持续30分钟, 名称=小兵甲
      { "id": 576, "count": 3, "duration": 3600, "name": "精英怪" },    // 召唤物ID=576, 数量=3, 持续1小时, 名称=精英怪
      { "id": 577, "count": 1, "duration": 7200, "name": "BOSS王" }     // 召唤物ID=577, 数量=1, 持续2小时, 名称=BOSS王
    ]
  },

  "skill2009": {
    "monsters": [
      { "id": 7, "count": 1, "duration": 1800, "name": "守护者" },      // 30分钟
      { "id": 577, "count": 2, "duration": 3600, "name": "战士" }       // 1小时
    ]
  },

  "skill2010": {
    "monsters": [
      { "id": 7, "count": 1, "duration": 5400, "name": "火龙" }         // 1.5小时
    ]
  },

  "skill2385": {
    "monsters": [
      { "id": 7, "count": 1, "duration": 7200, "name": "饕餮" }         // 2小时
    ]
  }

  // 时间换算参考:
  // 1800秒 = 30分钟
  // 3600秒 = 1小时
  // 5400秒 = 1.5小时
  // 7200秒 = 2小时
  // 10800秒 = 3小时

  // 注意:
  // 1. 每个召唤物都必须指定 duration，不再有默认时长
  // 2. name 属性是可选的，如果不设置则使用游戏默认名称
  // 3. name 支持中文字符，会显示在怪物头顶
}
