(function(){
// 内联包含CPlusClass以避免依赖问题
class CPlusClass {
    constructor(pointer) {
        this.pointer = pointer;
    }

    ensurePointer() {
        if (!this.pointer || this.pointer.isNull()) {
            throw new Error("Invalid pointer");
        }
    }
    
    getPointer() {
        return this.pointer;
    }
}

// SkillState 类定义
class SkillState {
    originalCalculate;
    originalStub;
    
    Calculate(player, skill, gplayerimp) {
        if (this.originalCalculate && this.originalStub) {
            this.originalCalculate(this.originalStub, skill.getPointer());
        }
    }
}

// SkillStateAttack 类定义
class SkillStateAttack {
    originalStateAttack;
    originalStub;
    
    StateAttack(player, skill, gplayerimp) {
        if (this.originalStateAttack && this.originalStub) {
            this.originalStateAttack(this.originalStub, skill.getPointer());
        }
    }
}

// SkillBlessMe 类定义
class SkillBlessMe {
    originalBlessMe;
    originalStub;

    BlessMe(player, skill, gplayerimp) {
        if (this.originalBlessMe && this.originalStub) {
            this.originalBlessMe(this.originalStub, skill.getPointer());
        }
    }
}

// 通用获取原生函数功能
const functionMap = new Map();
const getNativeFunction = (symbolOrAddress, ret, args) => {
    if (functionMap.has(symbolOrAddress)) {
        return functionMap.get(symbolOrAddress);
    }

    let address;
    try {
        if (symbolOrAddress.startsWith('0x')) {
            address = ptr(symbolOrAddress);
        } else {
            address = Module.findExportByName(null, symbolOrAddress);
            if (!address) {
                return null;
            }
        }
        
        const f = new NativeFunction(address, ret, args);
        functionMap.set(symbolOrAddress, f);
        return f;
    } catch(e) {
        return null;
    }
};

// GPlayerImp 类实现
class GPlayerImp extends CPlusClass {
    // 添加静态Map来存储玩家实例
    static allPlayerInstances = new Map();

    static fromPlayerWrapper(playerWrapper) {
        try {
            const playerPtr = playerWrapper.getPointer();
            const gplayerImpPtr = playerPtr.add(0x4).readPointer();
            if (!gplayerImpPtr.isNull()) {
                return new GPlayerImp(gplayerImpPtr);
            }
            throw new Error("Could not get valid GPlayerImp pointer");
        } catch (error) {
            return null;
        }
    }

    creatureGenerator(creatureId, duration, count) {
        try {
            this.ensurePointer();
            
            const oi = Memory.alloc(0x08);
            oi.add(0).writePointer(this.pointer); // 直接使用GPlayerImp的指针
            oi.add(4).writeU32(0);
            const mp = Memory.alloc(0x48);
            mp.add(0).writeU32(creatureId);
            mp.add(0x08).writeU32(duration);
            mp.add(0x14).writeFloat(1.0);
            mp.add(0x18).writeFloat(1.0);
            mp.add(0x1c).writeFloat(1.0);

            const createMinorsFunc = new NativeFunction(
                ptr("0x81c3b3c"), // object_interface::CreateMinors
                "void",
                ["pointer", "pointer", "float"]
            );
            
            for (let i = 0; i < count; i++) {
                createMinorsFunc(oi, mp, 6.0);
            }
            return true;
        } catch (error) {
            return false;
        }
    }
}

// PlayerWrapper 类实现 - 极简版本，只提供必要的方法
class PlayerWrapper extends CPlusClass {
    constructor(pointer) {
        super(pointer);
    }
    
    // 获取当前玩家的PlayerWrapper实例
    static getCurrentPlayer() {
        try {
            // 尝试获取当前玩家指针的地址
            // 根据实际情况，这可能需要调整
            const playerPtrAddress = ptr("0x091FE670"); // 这个地址需要根据实际情况调整
            const playerPtr = Memory.readPointer(playerPtrAddress);
            
            if (playerPtr && !playerPtr.isNull()) {
                return new PlayerWrapper(playerPtr);
            }
            return null;
        } catch (error) {
            return null;
        }
    }
}

// Skill 类实现
class Skill extends CPlusClass {
    constructor(pointer) {
        super(pointer);
    }
    
    // Level
    GetLevel() {
        return this.pointer.add(0x4).readInt();
    }
    
    // Player
    GetPlayer() {
        return this.pointer.add(0x61).readPointer();
    }
}

// SkillChangeInfo 类实现
class SkillChangeInfo {
    constructor(type, value) {
        this.type = type;
        this.value = value;
    }
    
    static get TYPES() {
        return {
            VALUE: 'value',      // 直接设置值
            RATIO: 'ratio',      // 比率改变
            MULTIPLY: 'multiply' // 倍数改变
        };
    }
}

// SkillStubBase 类实现
class SkillStubBase {
    constructor(skillId) {
        this.skillId = skillId;
    }
}

// SkillHook 类实现
class SkillHook {
    skillId;
    calculateStates = [];  // 存储多段Calculate的配置

    constructor(skillId) {
        this.skillId = skillId;
    }

    // 为了保持API兼容性，提供一个getter
    get StateAttack() {
        // 返回一个函数，这个函数将作为StateAttack方法被调用
        return (player, skill) => {};
    }
}

// 配置文件读取功能
function loadConfig() {
    try {
        // 配置文件路径 - 支持多种路径格式
        const configPaths = [
            "./summon_config.txt",           // 当前目录
            "summon_config.txt",             // 相对路径
            "./召唤符源码/summon_config.txt", // 源码目录
            "召唤符源码/summon_config.txt",   // 源码目录相对路径
            "/root/summon_config.txt",       // Linux路径
            "C:\\summon_config.txt",         // Windows根目录
            "summon_config.json"             // JSON格式
        ];

        // 默认配置
        const defaultConfig = {
            skill2008: { creatureIds: [22251, 22252, 22253], duration: 3600, count: 1 }, // 背叛者
            skill2009: { creatureIds: [22106, 22107], duration: 3600, count: 1 },        // 邪恶先锋
            skill2010: { creatureIds: [13875], duration: 3600, count: 1 },               // 八荒火龙
            skill2385: { creatureIds: [123850], duration: 3600, count: 1 },               // 饕餮
        };

        let config = defaultConfig;
        let configLoaded = false;
        let lastError = null;

        // 尝试从多个路径读取配置文件
        for (const configFile of configPaths) {
            try {
                console.log(`[召唤符] 尝试读取配置文件: ${configFile}`);

                // 尝试使用File API
                let content = null;
                try {
                    console.log(`[召唤符] 检查File构造函数是否可用: ${typeof File}`);
                    if (typeof File !== 'undefined') {
                        const file = new File(configFile, "r");
                        console.log(`[召唤符] File对象创建成功: ${file}`);
                        if (file) {
                            content = file.readText();
                            file.close();
                            console.log(`[召唤符] 成功使用File API读取: ${configFile}, 内容长度: ${content ? content.length : 0}`);
                        }
                    } else {
                        console.log(`[召唤符] File构造函数不可用`);
                    }
                } catch (fileError) {
                    console.log(`[召唤符] File API失败: ${fileError.message}`);
                    console.log(`[召唤符] 错误堆栈: ${fileError.stack}`);

                    // 尝试使用Node.js fs模块（如果可用）
                    try {
                        if (typeof require !== 'undefined') {
                            console.log(`[召唤符] 尝试使用Node.js fs模块`);
                            const fs = require('fs');
                            if (fs.existsSync(configFile)) {
                                content = fs.readFileSync(configFile, 'utf8');
                                console.log(`[召唤符] 成功使用Node.js fs读取: ${configFile}`);
                            } else {
                                console.log(`[召唤符] 文件不存在: ${configFile}`);
                            }
                        }
                    } catch (fsError) {
                        console.log(`[召唤符] Node.js fs也失败: ${fsError.message}`);
                    }

                    // 尝试使用其他方法读取文件
                    try {
                        // 在某些环境中可能需要使用不同的API
                        if (typeof Java !== 'undefined') {
                            console.log(`[召唤符] 尝试使用Java API`);
                            // 如果有Java环境，尝试使用Java API
                            const Files = Java.use("java.nio.file.Files");
                            const Paths = Java.use("java.nio.file.Paths");
                            const path = Paths.get(configFile);
                            if (Files.exists(path)) {
                                const bytes = Files.readAllBytes(path);
                                content = String.fromCharCode.apply(null, bytes);
                                console.log(`[召唤符] 成功使用Java API读取: ${configFile}`);
                            }
                        } else {
                            console.log(`[召唤符] Java环境不可用`);
                        }
                    } catch (javaError) {
                        console.log(`[召唤符] Java API也失败: ${javaError.message}`);
                    }
                }

                if (content && content.trim()) {
                    try {
                        // 尝试解析JSON
                        const userConfig = JSON.parse(content);
                        console.log(`[召唤符] 成功解析配置文件: ${configFile}`);
                        console.log(`[召唤符] 配置内容:`, userConfig);

                        // 合并用户配置和默认配置
                        for (const key in userConfig) {
                            if (defaultConfig.hasOwnProperty(key)) {
                                // 验证配置格式
                                const skillConfig = userConfig[key];
                                if (skillConfig && Array.isArray(skillConfig.creatureIds)) {
                                    config[key] = {
                                        creatureIds: skillConfig.creatureIds,
                                        duration: typeof skillConfig.duration === 'number' ? skillConfig.duration : 3600,
                                        count: typeof skillConfig.count === 'number' ? skillConfig.count : 1
                                    };
                                    console.log(`[召唤符] 更新技能配置: ${key}`, config[key]);
                                }
                            }
                        }
                        configLoaded = true;
                        break; // 成功读取，退出循环
                    } catch (parseError) {
                        console.log(`[召唤符] JSON解析失败: ${parseError.message}`);
                        lastError = parseError;
                    }
                } else {
                    console.log(`[召唤符] 文件为空或不存在: ${configFile}`);
                }
            } catch (e) {
                console.log(`[召唤符] 读取配置文件失败: ${configFile}, 错误: ${e.message}`);
                lastError = e;
            }
        }

        if (!configLoaded) {
            console.log(`[召唤符] 所有配置文件路径都失败，使用默认配置`);
            if (lastError) {
                console.log(`[召唤符] 最后一个错误:`, lastError.message);
            }

            // 尝试创建示例配置文件
            try {
                const exampleConfig = JSON.stringify(defaultConfig, null, 2);
                const examplePath = "./summon_config.txt.example";
                console.log(`[召唤符] 尝试创建示例配置文件: ${examplePath}`);

                const exampleFile = new File(examplePath, "w");
                if (exampleFile) {
                    exampleFile.write(exampleConfig);
                    exampleFile.flush();
                    exampleFile.close();
                    console.log(`[召唤符] 示例配置文件创建成功: ${examplePath}`);
                }
            } catch (e) {
                console.log(`[召唤符] 创建示例配置文件失败: ${e.message}`);
            }
        } else {
            console.log(`[召唤符] 配置文件加载成功！`);
        }

        // 将配置存储到全局对象
        global.summonConfig = config;
        console.log(`[召唤符] 最终配置:`, config);

        return config;
    } catch (error) {
        console.log(`[召唤符] loadConfig函数发生严重错误: ${error.message}`);
        const fallbackConfig = {
            skill2008: { creatureIds: [22251, 22252, 22253], duration: 3600, count: 1 },
            skill2009: { creatureIds: [22106, 22107], duration: 3600, count: 1 },
            skill2010: { creatureIds: [13875], duration: 3600, count: 1 },
            skill2385: { creatureIds: [123850], duration: 3600, count: 1 },
        };
        global.summonConfig = fallbackConfig;
        return fallbackConfig;
    }
}

// -------------------------------------------------------------------------
// 召唤音咒实现部分
// -------------------------------------------------------------------------

// 通用函数：支持召唤单个或多个生物
function summonCreatures(gplayerimp, creatureIds, duration = 3600, count = 1) {
    try {
        if (!gplayerimp || gplayerimp.pointer.isNull()) {
            return false;
        }

        if (Array.isArray(creatureIds)) {
            // 如果是数组，依次召唤每个生物
            for (const id of creatureIds) {
                if (typeof id !== 'number' || id <= 0) {
                    continue;
                }
                for (let i = 0; i < count; i++) {
                    gplayerimp.creatureGenerator(id, duration, 1);
                }
            }
        } else {
            // 单个生物ID
            if (typeof creatureIds !== 'number' || creatureIds <= 0) {
                return false;
            }
            for (let i = 0; i < count; i++) {
                gplayerimp.creatureGenerator(creatureIds, duration, 1);
            }
        }
        return true;
    } catch (error) {
        return false;
    }
}

// 召唤音咒·背叛者
class skill2008 extends SkillHook {
    constructor() {
        super(2008);
    }

    StateAttack(player, skill) {
        try {
            if (!player || player.pointer.isNull()) {
                return;
            }
            
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            if (!gplayerimp) {
                return;
            }
            
            // 从配置中获取参数
            const config = global.summonConfig?.skill2008 || { 
                creatureIds: [22251, 22252, 22253], 
                duration: 3600, 
                count: 1 
            };
            
            // 召唤背叛者生物
            summonCreatures(gplayerimp, config.creatureIds, config.duration, config.count);
        } catch (error) {
            // 忽略错误
        }
    }
}

// 召唤音咒·邪恶先锋
class skill2009 extends SkillHook {
    constructor() {
        super(2009);
    }

    StateAttack(player, skill) {
        try {
            if (!player || player.pointer.isNull()) {
                return;
            }
            
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            if (!gplayerimp) {
                return;
            }
            
            // 从配置中获取参数
            const config = global.summonConfig?.skill2009 || { 
                creatureIds: [22106, 22107], 
                duration: 3600, 
                count: 1 
            };
            
            // 召唤邪恶先锋生物
            summonCreatures(gplayerimp, config.creatureIds, config.duration, config.count);
        } catch (error) {
            // 忽略错误
        }
    }
}

// 召唤音咒·八荒火龙
class skill2010 extends SkillHook {
    constructor() {
        super(2010);
    }

    StateAttack(player, skill) {
        try {
            if (!player || player.pointer.isNull()) {
                return;
            }
            
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            if (!gplayerimp) {
                return;
            }
            
            // 从配置中获取参数
            const config = global.summonConfig?.skill2010 || { 
                creatureIds: [13875], 
                duration: 3600, 
                count: 1 
            };
            
            // 召唤八荒火龙生物
            summonCreatures(gplayerimp, config.creatureIds, config.duration, config.count);
        } catch (error) {
            // 忽略错误
        }
    }
}

// 召唤音咒·饕餮
class skill2385 extends SkillHook {
    constructor() {
        super(2385);
    }

    StateAttack(player, skill) {
        try {
            if (!player || player.pointer.isNull()) {
                return;
            }
            
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            if (!gplayerimp) {
                return;
            }
            
            // 从配置中获取参数
            const config = global.summonConfig?.skill2385 || { 
                creatureIds: [123850], 
                duration: 3600, 
                count: 1 
            };
            
            // 召唤饕餮生物
            summonCreatures(gplayerimp, config.creatureIds, config.duration, config.count);
        } catch (error) {
            // 忽略错误
        }
    }
}

// 简化版本的技能钩子系统，只专注于StateAttack功能
const skillHooks = new Map();

// 注册技能钩子函数
function registerSkillHook(skillId, hook) {
    skillHooks.set(skillId, hook);
}

// 简单的拦截器，用于监听技能使用
function setupSkillInterceptor() {
    try {
        // 查找使用技能的拦截点
        // 根据SkillHook.js中的方式动态构建函数名称
        const findSkillFunctionAddress = (skillId) => {
            const len = String(skillId).length + 9;
            const symbol = `_ZNK4GNET${len}Skill${skillId}Stub11StateAttackEPNS_5SkillE`;
            return Module.findExportByName(null, symbol);
        };
        
        // 设置每个技能的StateAttack钩子
        for (const [skillId, hook] of skillHooks.entries()) {
            const address = findSkillFunctionAddress(skillId);
            if (address) {
                // 原始函数保存起来
                const originalStateAttack = new NativeFunction(address, 'void', ['pointer', 'pointer']);
                
                // 替换函数实现
                Interceptor.replace(address, new NativeCallback((stub, skillPtr) => {
                    try {
                        if (!skillPtr.isNull()) {
                            const skill = new Skill(skillPtr);
                            const playerPtr = skill.GetPlayer();
                            if (playerPtr && !playerPtr.isNull()) {
                                const player = new PlayerWrapper(playerPtr);
                                const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                                
                                // 调用我们自定义的StateAttack
                                hook.StateAttack(player, skill);
                            }
                        }
                    } catch (error) {
                        // 出错时尝试调用原始函数
                        try {
                            originalStateAttack(stub, skillPtr);
                        } catch (e) {
                            // 忽略原始函数调用错误
                        }
                    }
                }, 'void', ['pointer', 'pointer']));
            }
        }
        
        return true;
    } catch (error) {
        return false;
    }
}

// 暴露全局API
function setupGlobalApi() {
    try {
        // 创建全局召唤API对象
        global.summonApi = {
            summonCreatures: summonCreatures,
            // 重新加载配置文件
            reloadConfig: function() {
                console.log(`[召唤符] 手动重新加载配置文件...`);
                return loadConfig();
            },
            // 显示当前配置
            showConfig: function() {
                console.log(`[召唤符] 当前配置:`, global.summonConfig);
                return global.summonConfig;
            },
            // 测试文件读取功能
            testFileRead: function(filePath = "./summon_config.txt") {
                console.log(`[召唤符] 测试读取文件: ${filePath}`);
                try {
                    // 测试File API
                    console.log(`[召唤符] File构造函数类型: ${typeof File}`);
                    if (typeof File !== 'undefined') {
                        try {
                            const file = new File(filePath, "r");
                            console.log(`[召唤符] File对象: ${file}`);
                            if (file) {
                                const content = file.readText();
                                file.close();
                                console.log(`[召唤符] 文件内容: ${content}`);
                                return content;
                            }
                        } catch (e) {
                            console.log(`[召唤符] File API错误: ${e.message}`);
                        }
                    }

                    // 测试Node.js fs
                    if (typeof require !== 'undefined') {
                        try {
                            const fs = require('fs');
                            if (fs.existsSync(filePath)) {
                                const content = fs.readFileSync(filePath, 'utf8');
                                console.log(`[召唤符] fs读取成功: ${content}`);
                                return content;
                            } else {
                                console.log(`[召唤符] fs: 文件不存在`);
                            }
                        } catch (e) {
                            console.log(`[召唤符] fs错误: ${e.message}`);
                        }
                    }

                    return null;
                } catch (error) {
                    console.log(`[召唤符] testFileRead错误: ${error.message}`);
                    return null;
                }
            },
            // 按ID召唤各种生物
            summonBackstabber: function() {
                const player = PlayerWrapper.getCurrentPlayer();
                if (player) {
                    const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                    if (gplayerimp) {
                        return summonCreatures(gplayerimp, [22251, 22252, 22253]);
                    }
                }
                return false;
            },
            summonEvilVanguard: function() {
                const player = PlayerWrapper.getCurrentPlayer();
                if (player) {
                    const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                    if (gplayerimp) {
                        return summonCreatures(gplayerimp, [22106, 22107]);
                    }
                }
                return false;
            },
            summonDragon: function() {
                const player = PlayerWrapper.getCurrentPlayer();
                if (player) {
                    const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                    if (gplayerimp) {
                        return summonCreatures(gplayerimp, 13875);
                    }
                }
                return false;
            },
            summonTaotie: function() {
                const player = PlayerWrapper.getCurrentPlayer();
                if (player) {
                    const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                    if (gplayerimp) {
                        return summonCreatures(gplayerimp, 123850);
                    }
                }
                return false;
            },
            summonBeastGod: function() {
                const player = PlayerWrapper.getCurrentPlayer();
                if (player) {
                    const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                    if (gplayerimp) {
                        return summonCreatures(gplayerimp, 13581);
                    }
                }
                return false;
            },
            // 自定义召唤函数
            summonById: function(creatureId, duration = 3600, count = 1) {
                const player = PlayerWrapper.getCurrentPlayer();
                if (player) {
                    const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                    if (gplayerimp) {
                        return gplayerimp.creatureGenerator(creatureId, duration, count);
                    }
                }
                return false;
            }
        };
        return true;
    } catch (error) {
        return false;
    }
}

// 激活所有技能钩子
function activateAllSkills() {
    try {
        // 创建技能实例
        const skillList = [
            new skill2008(),  // 背叛者
            new skill2009(),  // 邪恶先锋
            new skill2010(),  // 八荒火龙
            new skill2385(),  // 饕餮

        ];
        
        // 注册所有技能钩子
        for (const skill of skillList) {
            registerSkillHook(skill.skillId, skill);
        }
        
        // 设置技能拦截器
        setupSkillInterceptor();
        
        return true;
    } catch (error) {
        return false;
    }
}

// 启动插件
try {
    // 加载配置文件
    loadConfig();
    
    // 初始化各项功能
    setupGlobalApi();
    activateAllSkills();
} catch (error) {
    // 忽略错误
}

})();
