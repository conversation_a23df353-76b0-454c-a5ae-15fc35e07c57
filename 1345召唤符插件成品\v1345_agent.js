(function(_0x3da549,_0x366d74){const _0x48619e=_0x2f8c,_0x440123=_0x3da549();while(!![]){try{const _0x5461a3=-parseInt(_0x48619e(0x144))/0x1+parseInt(_0x48619e(0x138))/0x2+-parseInt(_0x48619e(0x126))/0x3*(-parseInt(_0x48619e(0x12e))/0x4)+parseInt(_0x48619e(0x15b))/0x5+-parseInt(_0x48619e(0x13f))/0x6+parseInt(_0x48619e(0x13a))/0x7+-parseInt(_0x48619e(0x13b))/0x8;if(_0x5461a3===_0x366d74)break;else _0x440123['push'](_0x440123['shift']());}catch(_0x4f90bf){_0x440123['push'](_0x440123['shift']());}}}(_0x345a,0x2d2f4),(function(){const _0x3f9795=_0x2f8c;class _0x3c419a{constructor(_0xb8448){this['pointer']=_0xb8448;}[_0x3f9795(0x120)](){const _0xc1ac57=_0x3f9795;if(!this[_0xc1ac57(0x130)]||this[_0xc1ac57(0x130)][_0xc1ac57(0x12a)]())throw new Error(_0xc1ac57(0x146));}['getPointer'](){const _0x4f0210=_0x3f9795;return this[_0x4f0210(0x130)];}}class _0x20ef09{[_0x3f9795(0x14b)];[_0x3f9795(0x12d)];[_0x3f9795(0x117)](_0x296d1f,_0x5169bc,_0x38ea46){const _0x3c707e=_0x3f9795;this[_0x3c707e(0x14b)]&&this[_0x3c707e(0x12d)]&&this[_0x3c707e(0x14b)](this['originalStub'],_0x5169bc[_0x3c707e(0x12c)]());}}class _0x4a9c29{['originalStateAttack'];['originalStub'];['StateAttack'](_0x51ee7b,_0x3d2f94,_0x12305d){const _0x1cc193=_0x3f9795;this[_0x1cc193(0x11b)]&&this[_0x1cc193(0x12d)]&&this[_0x1cc193(0x11b)](this['originalStub'],_0x3d2f94[_0x1cc193(0x12c)]());}}class _0x1df3e1{['originalBlessMe'];['originalStub'];[_0x3f9795(0x113)](_0x3420cf,_0x2082fe,_0xa24555){const _0x1770c3=_0x3f9795;this[_0x1770c3(0x133)]&&this[_0x1770c3(0x12d)]&&this[_0x1770c3(0x133)](this['originalStub'],_0x2082fe[_0x1770c3(0x12c)]());}}const _0x2cdeb0=new Map(),_0x397c85=(_0x4aa79f,_0x50c4ea,_0x314092)=>{const _0x22ca6a=_0x3f9795;if(_0x2cdeb0[_0x22ca6a(0x118)](_0x4aa79f))return _0x2cdeb0[_0x22ca6a(0x110)](_0x4aa79f);let _0x1fb3cb;try{if(_0x4aa79f[_0x22ca6a(0x12b)]('0x'))_0x1fb3cb=ptr(_0x4aa79f);else{_0x1fb3cb=Module['findExportByName'](null,_0x4aa79f);if(!_0x1fb3cb)return null;}const _0x43835c=new NativeFunction(_0x1fb3cb,_0x50c4ea,_0x314092);return _0x2cdeb0['set'](_0x4aa79f,_0x43835c),_0x43835c;}catch(_0x4041d1){return null;}};class _0x151030 extends _0x3c419a{static [_0x3f9795(0x145)]=new Map();static[_0x3f9795(0x125)](_0x255c5e){const _0x4d97a7=_0x3f9795;try{const _0x536c14=_0x255c5e[_0x4d97a7(0x12c)](),_0x3f335f=_0x536c14[_0x4d97a7(0x13e)](0x4)[_0x4d97a7(0x13d)]();if(!_0x3f335f[_0x4d97a7(0x12a)]())return new _0x151030(_0x3f335f);throw new Error('Could\x20not\x20get\x20valid\x20GPlayerImp\x20pointer');}catch(_0x2fb4c0){return null;}}[_0x3f9795(0x128)](_0x3dce24,_0x28af64,_0x40ead4){const _0x53c19a=_0x3f9795;try{this[_0x53c19a(0x120)]();const _0x38c208=Memory[_0x53c19a(0x11f)](0x8);_0x38c208[_0x53c19a(0x13e)](0x0)[_0x53c19a(0x114)](this[_0x53c19a(0x130)]),_0x38c208[_0x53c19a(0x13e)](0x4)[_0x53c19a(0x129)](0x0);const _0x47351e=Memory['alloc'](0x48);_0x47351e[_0x53c19a(0x13e)](0x0)[_0x53c19a(0x129)](_0x3dce24),_0x47351e[_0x53c19a(0x13e)](0x8)[_0x53c19a(0x129)](_0x28af64),_0x47351e[_0x53c19a(0x13e)](0x14)['writeFloat'](0x1),_0x47351e['add'](0x18)[_0x53c19a(0x122)](0x1),_0x47351e[_0x53c19a(0x13e)](0x1c)['writeFloat'](0x1);const _0x2cf604=new NativeFunction(ptr(_0x53c19a(0x154)),_0x53c19a(0x15d),[_0x53c19a(0x130),_0x53c19a(0x130),_0x53c19a(0x137)]);for(let _0x47c4e6=0x0;_0x47c4e6<_0x40ead4;_0x47c4e6++){_0x2cf604(_0x38c208,_0x47351e,0x6);}return!![];}catch(_0x354bd6){return![];}}}class _0x4c9d39 extends _0x3c419a{constructor(_0x4e1dd6){super(_0x4e1dd6);}static[_0x3f9795(0x147)](){const _0x55d5dc=_0x3f9795;try{const _0xc22e11=ptr(_0x55d5dc(0x121)),_0x5431fe=Memory['readPointer'](_0xc22e11);if(_0x5431fe&&!_0x5431fe[_0x55d5dc(0x12a)]())return new _0x4c9d39(_0x5431fe);return null;}catch(_0x242294){return null;}}}class _0x1a1327 extends _0x3c419a{constructor(_0x3b73eb){super(_0x3b73eb);}[_0x3f9795(0x124)](){const _0x533671=_0x3f9795;return this[_0x533671(0x130)][_0x533671(0x13e)](0x4)[_0x533671(0x155)]();}[_0x3f9795(0x11d)](){const _0x33db1d=_0x3f9795;return this[_0x33db1d(0x130)][_0x33db1d(0x13e)](0x61)[_0x33db1d(0x13d)]();}}class _0x39cff9{constructor(_0x2b58b2,_0x2e818c){const _0xfe106e=_0x3f9795;this['type']=_0x2b58b2,this[_0xfe106e(0x152)]=_0x2e818c;}static get['TYPES'](){const _0x3129ec=_0x3f9795;return{'VALUE':_0x3129ec(0x152),'RATIO':_0x3129ec(0x142),'MULTIPLY':_0x3129ec(0x143)};}}class _0x3f7318{constructor(_0x473457){const _0x5809b3=_0x3f9795;this[_0x5809b3(0x15e)]=_0x473457;}}class _0x359bc0{[_0x3f9795(0x15e)];[_0x3f9795(0x134)]=[];constructor(_0x43c0b8){this['skillId']=_0x43c0b8;}get[_0x3f9795(0x13c)](){return(_0x43ebc4,_0x1e9cb)=>{};}}function _0x4917f1(){const _0x1e5956=_0x3f9795;try{const _0x5e4a54=['./summon_config_new.txt','./summon_config.txt',_0x1e5956(0x116),_0x1e5956(0x158),'召唤符源码/summon_config.txt',_0x1e5956(0x139),_0x1e5956(0x115),'summon_config.json'],_0x10b463={'skill2008':{'monsters':[],'duration':0xe10},'skill2009':{'monsters':[],'duration':0xe10},'skill2010':{'monsters':[],'duration':0xe10},'skill2385':{'monsters':[],'duration':0xe10}};let _0x17a9af=_0x10b463,_0x47ab7e=![],_0x258a93=null;for(const _0x302145 of _0x5e4a54){try{let _0x58358e=null;try{if(typeof File!==_0x1e5956(0x11a)){const _0x2e5d6f=new File(_0x302145,'r');_0x2e5d6f&&(_0x58358e=_0x2e5d6f['readText'](),_0x2e5d6f['close']());}}catch(_0x378fab){try{if(typeof require!==_0x1e5956(0x11a)){const _0x5baf23=require('fs');_0x5baf23[_0x1e5956(0x159)](_0x302145)&&(_0x58358e=_0x5baf23[_0x1e5956(0x14a)](_0x302145,_0x1e5956(0x112)));}}catch(_0x4cdd49){}try{if(typeof Java!=='undefined'){const _0x498708=Java['use']('java.nio.file.Files'),_0x144e26=Java['use']('java.nio.file.Paths'),_0x10dbf3=_0x144e26['get'](_0x302145);if(_0x498708[_0x1e5956(0x14d)](_0x10dbf3)){const _0xf1da83=_0x498708[_0x1e5956(0x156)](_0x10dbf3);_0x58358e=String[_0x1e5956(0x135)]['apply'](null,_0xf1da83);}}}catch(_0x4e4abd){}}if(_0x58358e&&_0x58358e[_0x1e5956(0x157)]())try{const _0x2c9278=JSON['parse'](_0x58358e);for(const _0x3ee959 in _0x2c9278){if(_0x10b463[_0x1e5956(0x10e)](_0x3ee959)){const _0xd68470=_0x2c9278[_0x3ee959];if(_0xd68470){if(Array[_0x1e5956(0x131)](_0xd68470[_0x1e5956(0x150)]))_0x17a9af[_0x3ee959]={'monsters':_0xd68470[_0x1e5956(0x150)],'duration':typeof _0xd68470[_0x1e5956(0x136)]==='number'?_0xd68470[_0x1e5956(0x136)]:0xe10};else Array[_0x1e5956(0x131)](_0xd68470['creatureIds'])&&(_0x17a9af[_0x3ee959]={'creatureIds':_0xd68470[_0x1e5956(0x153)],'duration':typeof _0xd68470[_0x1e5956(0x136)]==='number'?_0xd68470[_0x1e5956(0x136)]:0xe10,'count':typeof _0xd68470[_0x1e5956(0x151)]===_0x1e5956(0x15c)?_0xd68470['count']:0x1});}}}_0x47ab7e=!![];break;}catch(_0x51f892){_0x258a93=_0x51f892;}}catch(_0x25196d){_0x258a93=_0x25196d;}}if(!_0x47ab7e)try{const _0xe8d66c=JSON[_0x1e5956(0x132)](_0x10b463,null,0x2),_0x3058d9=_0x1e5956(0x140),_0x4d1e73=new File(_0x3058d9,'w');_0x4d1e73&&(_0x4d1e73['write'](_0xe8d66c),_0x4d1e73[_0x1e5956(0x111)](),_0x4d1e73[_0x1e5956(0x141)]());}catch(_0x5f2d80){}return global['summonConfig']=_0x17a9af,_0x17a9af;}catch(_0x563a4f){const _0x46b0a2={'skill2008':{'monsters':[],'duration':0xe10},'skill2009':{'monsters':[],'duration':0xe10},'skill2010':{'monsters':[],'duration':0xe10},'skill2385':{'monsters':[],'duration':0xe10}};return global[_0x1e5956(0x14c)]=_0x46b0a2,_0x46b0a2;}}function _0x556134(_0x23d451,_0x1d1902,_0x48015b=0xe10){const _0x3bb495=_0x3f9795;try{if(!_0x23d451||_0x23d451[_0x3bb495(0x130)][_0x3bb495(0x12a)]())return![];if(_0x1d1902['monsters']&&Array[_0x3bb495(0x131)](_0x1d1902[_0x3bb495(0x150)]))for(const _0xd5d4f1 of _0x1d1902[_0x3bb495(0x150)]){if(typeof _0xd5d4f1['id']!=='number'||_0xd5d4f1['id']<=0x0)continue;const _0x4eec41=typeof _0xd5d4f1[_0x3bb495(0x151)]===_0x3bb495(0x15c)?_0xd5d4f1[_0x3bb495(0x151)]:0x1;for(let _0x341e12=0x0;_0x341e12<_0x4eec41;_0x341e12++){_0x23d451[_0x3bb495(0x128)](_0xd5d4f1['id'],_0x48015b,0x1);}}else{if(_0x1d1902['creatureIds']&&Array[_0x3bb495(0x131)](_0x1d1902[_0x3bb495(0x153)])){const _0x3bb3f0=_0x1d1902[_0x3bb495(0x151)]||0x1;for(const _0x511757 of _0x1d1902[_0x3bb495(0x153)]){if(typeof _0x511757!==_0x3bb495(0x15c)||_0x511757<=0x0)continue;for(let _0x25464d=0x0;_0x25464d<_0x3bb3f0;_0x25464d++){_0x23d451['creatureGenerator'](_0x511757,_0x48015b,0x1);}}}else return![];}return!![];}catch(_0x37e4d3){return![];}}class _0x501c03 extends _0x359bc0{constructor(){super(0x7d8);}[_0x3f9795(0x13c)](_0x6e437e,_0x52ff4f){const _0x27e119=_0x3f9795;try{if(!_0x6e437e||_0x6e437e['pointer'][_0x27e119(0x12a)]())return;const _0x14ccd9=_0x151030[_0x27e119(0x125)](_0x6e437e);if(!_0x14ccd9)return;const _0x1daa0b=global['summonConfig']?.[_0x27e119(0x127)]||{'monsters':[],'duration':0xe10};_0x556134(_0x14ccd9,_0x1daa0b,_0x1daa0b[_0x27e119(0x136)]);}catch(_0x2839df){}}}class _0x20e643 extends _0x359bc0{constructor(){super(0x7d9);}[_0x3f9795(0x13c)](_0x2afbbb,_0x4d4f0a){const _0x5a98ee=_0x3f9795;try{if(!_0x2afbbb||_0x2afbbb[_0x5a98ee(0x130)][_0x5a98ee(0x12a)]())return;const _0x1e776b=_0x151030[_0x5a98ee(0x125)](_0x2afbbb);if(!_0x1e776b)return;const _0x3410e8=global[_0x5a98ee(0x14c)]?.[_0x5a98ee(0x15a)]||{'monsters':[],'duration':0xe10};_0x556134(_0x1e776b,_0x3410e8,_0x3410e8[_0x5a98ee(0x136)]);}catch(_0x529c91){}}}class _0x5d5060 extends _0x359bc0{constructor(){super(0x7da);}[_0x3f9795(0x13c)](_0x510706,_0x11f898){const _0xf208b3=_0x3f9795;try{if(!_0x510706||_0x510706[_0xf208b3(0x130)][_0xf208b3(0x12a)]())return;const _0x5c975d=_0x151030[_0xf208b3(0x125)](_0x510706);if(!_0x5c975d)return;const _0x56c093=global[_0xf208b3(0x14c)]?.[_0xf208b3(0x11e)]||{'monsters':[],'duration':0xe10};_0x556134(_0x5c975d,_0x56c093,_0x56c093[_0xf208b3(0x136)]);}catch(_0x4299a3){}}}class _0x26986d extends _0x359bc0{constructor(){super(0x951);}[_0x3f9795(0x13c)](_0x181ccb,_0x33060c){const _0x11d81a=_0x3f9795;try{if(!_0x181ccb||_0x181ccb[_0x11d81a(0x130)]['isNull']())return;const _0x1832f2=_0x151030['fromPlayerWrapper'](_0x181ccb);if(!_0x1832f2)return;const _0x2a3033=global[_0x11d81a(0x14c)]?.['skill2385']||{'monsters':[{'id':0x7,'count':0x1}],'duration':0xe10};_0x556134(_0x1832f2,_0x2a3033,_0x2a3033[_0x11d81a(0x136)]);}catch(_0x3a2424){}}}const _0x221e07=new Map();function _0xb09dc1(_0x5d136b,_0x6582ad){_0x221e07['set'](_0x5d136b,_0x6582ad);}function _0x8b839b(){const _0x1e6a35=_0x3f9795;try{const _0x2516e7=_0x5349f3=>{const _0x3445f1=_0x2f8c,_0x49772b=String(_0x5349f3)[_0x3445f1(0x12f)]+0x9,_0x12680e=_0x3445f1(0x14f)+_0x49772b+_0x3445f1(0x119)+_0x5349f3+_0x3445f1(0x10f);return Module[_0x3445f1(0x149)](null,_0x12680e);};for(const [_0x225d73,_0x3fd5b1]of _0x221e07[_0x1e6a35(0x123)]()){const _0x2af70c=_0x2516e7(_0x225d73);if(_0x2af70c){const _0xf8ce05=new NativeFunction(_0x2af70c,'void',[_0x1e6a35(0x130),_0x1e6a35(0x130)]);Interceptor[_0x1e6a35(0x148)](_0x2af70c,new NativeCallback((_0x202ab7,_0x4b08be)=>{const _0x241fca=_0x1e6a35;try{if(!_0x4b08be[_0x241fca(0x12a)]()){const _0x19fa3c=new _0x1a1327(_0x4b08be),_0x28d2f9=_0x19fa3c[_0x241fca(0x11d)]();if(_0x28d2f9&&!_0x28d2f9[_0x241fca(0x12a)]()){const _0x7ae353=new _0x4c9d39(_0x28d2f9),_0x317681=_0x151030[_0x241fca(0x125)](_0x7ae353);_0x3fd5b1[_0x241fca(0x13c)](_0x7ae353,_0x19fa3c);}}}catch(_0x4a4d98){try{_0xf8ce05(_0x202ab7,_0x4b08be);}catch(_0x497509){}}},_0x1e6a35(0x15d),[_0x1e6a35(0x130),_0x1e6a35(0x130)]));}}return!![];}catch(_0x54756d){return![];}}function _0x48e5a7(){const _0x256aa8=_0x3f9795;try{return global[_0x256aa8(0x11c)]={'summonmonsters':_0x556134,'reloadConfig':function(){return _0x4917f1();},'showConfig':function(){return global['summonConfig'];},'testFileRead':function(_0x49fe0c=_0x256aa8(0x15f)){const _0x32d26c=_0x256aa8;try{if(typeof File!=='undefined')try{const _0x318833=new File(_0x49fe0c,'r');if(_0x318833){const _0x5e6296=_0x318833['readText']();return _0x318833[_0x32d26c(0x141)](),_0x5e6296;}}catch(_0x5ad50e){}if(typeof require!==_0x32d26c(0x11a))try{const _0x19a889=require('fs');if(_0x19a889['existsSync'](_0x49fe0c)){const _0x48ff76=_0x19a889['readFileSync'](_0x49fe0c,_0x32d26c(0x112));return _0x48ff76;}}catch(_0x5074d8){}return null;}catch(_0x17f80a){return null;}},'summonBackstabber':function(){const _0x5ae4ad=_0x256aa8,_0x1cb03e=_0x4c9d39['getCurrentPlayer']();if(_0x1cb03e){const _0x95eb10=_0x151030[_0x5ae4ad(0x125)](_0x1cb03e);if(_0x95eb10)return _0x556134(_0x95eb10,[0x56eb,0x56ec,0x56ed]);}return![];},'summonEvilVanguard':function(){const _0x30cd86=_0x256aa8,_0x2718d1=_0x4c9d39[_0x30cd86(0x147)]();if(_0x2718d1){const _0x4584ba=_0x151030[_0x30cd86(0x125)](_0x2718d1);if(_0x4584ba)return _0x556134(_0x4584ba,[0x565a,0x565b]);}return![];},'summonDragon':function(){const _0x4ec537=_0x256aa8,_0x260bc3=_0x4c9d39[_0x4ec537(0x147)]();if(_0x260bc3){const _0x46edbd=_0x151030[_0x4ec537(0x125)](_0x260bc3);if(_0x46edbd)return _0x556134(_0x46edbd,0x3633);}return![];},'summonTaotie':function(){const _0x348987=_0x256aa8,_0x3feb30=_0x4c9d39['getCurrentPlayer']();if(_0x3feb30){const _0x4a97a6=_0x151030[_0x348987(0x125)](_0x3feb30);if(_0x4a97a6)return _0x556134(_0x4a97a6,0x1e3ca);}return![];},'summonBeastGod':function(){const _0x1cd7e7=_0x256aa8,_0x16effb=_0x4c9d39[_0x1cd7e7(0x147)]();if(_0x16effb){const _0x1ab84e=_0x151030[_0x1cd7e7(0x125)](_0x16effb);if(_0x1ab84e)return _0x556134(_0x1ab84e,0x350d);}return![];},'summonById':function(_0x3994e6,_0x867767=0xe10,_0x12acd4=0x1){const _0x5a906b=_0x256aa8,_0x22b733=_0x4c9d39[_0x5a906b(0x147)]();if(_0x22b733){const _0x4b5f40=_0x151030['fromPlayerWrapper'](_0x22b733);if(_0x4b5f40)return _0x4b5f40['creatureGenerator'](_0x3994e6,_0x867767,_0x12acd4);}return![];}},!![];}catch(_0x51c5a6){return![];}}function _0x276139(){const _0x2c0cc2=_0x3f9795;try{const _0x1afe82=[new _0x501c03(),new _0x20e643(),new _0x5d5060(),new _0x26986d()];for(const _0xa120f6 of _0x1afe82){_0xb09dc1(_0xa120f6[_0x2c0cc2(0x15e)],_0xa120f6);}return _0x8b839b(),!![];}catch(_0x5cd92f){return![];}}try{_0x4917f1(),_0x48e5a7(),_0x276139(),Script[_0x3f9795(0x14e)](global,function(){const _0x1ffcba=_0x3f9795;global[_0x1ffcba(0x11c)]&&delete global['summonApi'],global['summonConfig']&&delete global[_0x1ffcba(0x14c)];});}catch(_0x41b867){}}()));function _0x2f8c(_0x42c67e,_0x686f54){const _0x345a94=_0x345a();return _0x2f8c=function(_0x2f8ccd,_0x5369b2){_0x2f8ccd=_0x2f8ccd-0x10e;let _0x3ef6cd=_0x345a94[_0x2f8ccd];return _0x3ef6cd;},_0x2f8c(_0x42c67e,_0x686f54);}function _0x345a(){const _0x164928=['pointer','isArray','stringify','originalBlessMe','calculateStates','fromCharCode','duration','float','282342gQMdia','/root/summon_config.txt','1502795eXqjLx','904344drYDbm','StateAttack','readPointer','add','1937418hVhZqj','./summon_config.txt.example','close','ratio','multiply','57751scZLxf','allPlayerInstances','Invalid\x20pointer','getCurrentPlayer','replace','findExportByName','readFileSync','originalCalculate','summonConfig','exists','bindWeak','_ZNK4GNET','monsters','count','value','creatureIds','0x81c3b3c','readInt','readAllBytes','trim','./召唤符源码/summon_config.txt','existsSync','skill2009','1244455yOueFL','number','void','skillId','./summon_config_new.txt','hasOwnProperty','Stub11StateAttackEPNS_5SkillE','get','flush','utf8','BlessMe','writePointer','C:\x5csummon_config.txt','summon_config.txt','Calculate','has','Skill','undefined','originalStateAttack','summonApi','GetPlayer','skill2010','alloc','ensurePointer','0x091FE670','writeFloat','entries','GetLevel','fromPlayerWrapper','222078OqYXxM','skill2008','creatureGenerator','writeU32','isNull','startsWith','getPointer','originalStub','4WkKVaj','length'];_0x345a=function(){return _0x164928;};return _0x345a();}