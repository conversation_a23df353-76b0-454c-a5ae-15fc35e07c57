(function(){
    class CPlusClass {
        constructor(pointer) {
            this.pointer = pointer;
        }

        getPointer() {
            return this.pointer;
        }
    }

    class GPlayerImp extends CPlusClass {
        static fromPlayerWrapper(playerWrapper) {
            const playerPtr = playerWrapper.getPointer();
            const gplayerImpPtr = playerPtr.add(0x4).readPointer();
            return new GPlayerImp(gplayerImpPtr);
        }

        creatureGenerator(creatureId, duration, count) {
            const oi = Memory.alloc(0x08);
            oi.add(0).writePointer(this.pointer);
            oi.add(4).writeU32(0);
            const mp = Memory.alloc(0x48);
            mp.add(0).writeU32(creatureId);
            mp.add(0x08).writeU32(duration);
            mp.add(0x14).writeFloat(1.0);
            mp.add(0x18).writeFloat(1.0);
            mp.add(0x1c).writeFloat(1.0);

            const createMinorsFunc = new NativeFunction(
                ptr("0x81c3b3c"),
                "void",
                ["pointer", "pointer", "float"]
            );

            for (let i = 0; i < count; i++) {
                createMinorsFunc(oi, mp, 6.0);
            }
        }
    }

    class PlayerWrapper extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }
    }

    class Skill extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }

        GetPlayer() {
            return this.pointer.add(0x61).readPointer();
        }
    }

    class SkillHook {
        constructor(skillId) {
            this.skillId = skillId;
        }
    }
    
    function loadConfig() {
        const defaultConfig = {
            skill2008: { monsters: [{ id: 7, count: 1 }], duration: 3600 },
            skill2009: { monsters: [{ id: 7, count: 1 }], duration: 3600 },
            skill2010: { monsters: [{ id: 7, count: 1 }], duration: 3600 },
            skill2385: { monsters: [{ id: 7, count: 1 }], duration: 3600 }
        };

        const file = new File("/root/summon_config.txt", "r");
        const content = file.readText();
        file.close();
        const config = JSON.parse(content);

        // 确保每个技能都有配置
        for (const skillKey in defaultConfig) {
            if (!config[skillKey]) {
                config[skillKey] = defaultConfig[skillKey];
            }
        }

        global.summonConfig = config;
        console.log("配置加载成功:", JSON.stringify(config, null, 2));
        return config;
    }
    
    // -------------------------------------------------------------------------
    // 召唤音咒实现部分
    // -------------------------------------------------------------------------
    
    function summonmonsters(gplayerimp, config, duration) {
        console.log("召唤函数调用，配置:", JSON.stringify(config, null, 2));

        if (!config || !config.monsters || !Array.isArray(config.monsters)) {
            console.log("配置错误: monsters 不存在或不是数组");
            return;
        }

        for (const creature of config.monsters) {
            console.log("召唤生物:", creature);
            for (let i = 0; i < creature.count; i++) {
                gplayerimp.creatureGenerator(creature.id, duration, 1);
            }
        }
    }
    
    class skill2008 extends SkillHook {
        constructor() {
            super(2008);
        }

        StateAttack(player) {
            console.log("技能2008触发");
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill2008;
            console.log("skill2008配置:", config);
            summonmonsters(gplayerimp, config, config.duration);
        }
    }

    class skill2009 extends SkillHook {
        constructor() {
            super(2009);
        }

        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill2009;
            if (config && config.duration) {
                summonmonsters(gplayerimp, config, config.duration);
            }
        }
    }

    class skill2010 extends SkillHook {
        constructor() {
            super(2010);
        }

        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill2010;
            if (config && config.duration) {
                summonmonsters(gplayerimp, config, config.duration);
            }
        }
    }

    class skill2385 extends SkillHook {
        constructor() {
            super(2385);
        }

        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill2385;
            if (config && config.duration) {
                summonmonsters(gplayerimp, config, config.duration);
            }
        }
    }
    
    const skillHooks = new Map();

    function registerSkillHook(skillId, hook) {
        skillHooks.set(skillId, hook);
    }

    function setupSkillInterceptor() {
        const findSkillFunctionAddress = (skillId) => {
            const len = String(skillId).length + 9;
            const symbol = `_ZNK4GNET${len}Skill${skillId}Stub11StateAttackEPNS_5SkillE`;
            return Module.findExportByName(null, symbol);
        };

        for (const [skillId, hook] of skillHooks.entries()) {
            const address = findSkillFunctionAddress(skillId);
            if (address) {
                Interceptor.replace(address, new NativeCallback((stub, skillPtr) => {
                    const skill = new Skill(skillPtr);
                    const playerPtr = skill.GetPlayer();
                    const player = new PlayerWrapper(playerPtr);
                    hook.StateAttack(player);
                }, 'void', ['pointer', 'pointer']));
            }
        }
    }

    function activateAllSkills() {
        const skillList = [
            new skill2008(),
            new skill2009(),
            new skill2010(),
            new skill2385()
        ];

        for (const skill of skillList) {
            registerSkillHook(skill.skillId, skill);
        }

        setupSkillInterceptor();
    }

    // 聊天过滤功能
    function loadFilterConfig() {
        const file = new File("/root/filter_words.txt", "r");
        const content = file.readText();
        file.close();

        const config = {
            wordMap: {},
            filterNumber: true,
            enabled: true,
            defaultReplacement: '请文明游戏'
        };

        const lines = content.split('\n');
        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed || trimmed.startsWith('#')) continue;

            const parts = trimmed.split(',');
            if (parts.length >= 2) {
                const keyword = parts[0].trim();
                const replacement = parts[1].trim() || config.defaultReplacement;
                config.wordMap[keyword] = replacement;
            }
        }

        return config;
    }

    function filterMessage(msg) {
        const config = loadFilterConfig();

        if (!config.enabled) return msg;

        // 检查数字
        if (config.filterNumber && /\d{8,}/.test(msg)) {
            return '请不要发布联系方式';
        }

        // 替换违禁词
        let result = msg;
        for (const keyword in config.wordMap) {
            if (msg.includes(keyword)) {
                result = result.replace(new RegExp(keyword, 'g'), config.wordMap[keyword]);
            }
        }

        return result;
    }

    function setupChatFilter() {
        Interceptor.attach(ptr('0x08059EE0'), {
            onEnter(args) {
                const msg = args[3];
                const msg_length = args[4].toInt32();

                if (!msg || msg.isNull() || msg_length <= 0) return;

                const msgContent = msg.readUtf16String(msg_length / 2);
                if (!msgContent) return;

                const filtered = filterMessage(msgContent);

                if (filtered !== msgContent) {
                    const newMsgLen = filtered.length * 2;
                    const newMsgBuf = Memory.alloc(newMsgLen + 2);
                    newMsgBuf.writeUtf16String(filtered);

                    args[3] = newMsgBuf;
                    args[4] = ptr(newMsgLen);
                }
            }
        });
    }

    loadConfig();
    activateAllSkills();
    setupChatFilter();

    })();
    