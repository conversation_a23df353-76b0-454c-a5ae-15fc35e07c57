(function(){
    // ========================================================================
    // 版本适配配置 - 修改这里的地址和偏移值来适配不同版本
    // ========================================================================
    const VERSION_CONFIG = {
        // 召唤相关地址
        CREATE_MINORS_ADDRESS: "0x0813BD8C",        // object_interface::CreateMinors 函数地址  422:0x0813BD8C 1345:0x81c3b3c

        // 聊天过滤地址
        CHAT_FILTER_ADDRESS: "0x08059EE0",         // 聊天消息处理函数地址

        // 玩家对象偏移
        PLAYER_GPLAYERIMP_OFFSET: 0x4,             // PlayerWrapper -> GPlayerImp 偏移
        SKILL_PLAYER_OFFSET: 0x61,                 // Skill -> Player 偏移

        // 配置文件路径
        SUMMON_CONFIG_PATH: "/root/summon_config.txt",
        FILTER_CONFIG_PATH: "/root/filter_words.txt"
    };
    // ========================================================================

    class CPlusClass {
        constructor(pointer) {
            this.pointer = pointer;
        }
        getPointer() {
            return this.pointer;
        }
    }

    class GPlayerImp extends CPlusClass {
        static fromPlayerWrapper(playerWrapper) {
            const playerPtr = playerWrapper.getPointer();
            const gplayerImpPtr = playerPtr.add(VERSION_CONFIG.PLAYER_GPLAYERIMP_OFFSET).readPointer();
            return new GPlayerImp(gplayerImpPtr);
        }

        creatureGenerator(creatureId, duration, count) {
            const oi = Memory.alloc(0x08);
            oi.add(0).writePointer(this.pointer);
            oi.add(4).writeU32(0);
            const mp = Memory.alloc(0x48);
            mp.add(0).writeU32(creatureId);
            mp.add(0x08).writeU32(duration);
            mp.add(0x14).writeFloat(1.0);
            mp.add(0x18).writeFloat(1.0);
            mp.add(0x1c).writeFloat(1.0);

            const createMinorsFunc = new NativeFunction(
                ptr(VERSION_CONFIG.CREATE_MINORS_ADDRESS),
                "void",
                ["pointer", "pointer", "float"]
            );

            for (let i = 0; i < count; i++) {
                createMinorsFunc(oi, mp, 6.0);
            }
        }
    }

    class PlayerWrapper extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }
    }

    class Skill extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }
        GetPlayer() {
            return this.pointer.add(VERSION_CONFIG.SKILL_PLAYER_OFFSET).readPointer();
        }
    }

    class SkillHook {
        constructor(skillId) {
            this.skillId = skillId;
        }
    }
    
    function loadConfig() {
        const file = new File(VERSION_CONFIG.SUMMON_CONFIG_PATH, "r");
        const content = file.readText();
        file.close();
        const config = JSON.parse(content);
        global.summonConfig = config;
        return config;
    }
    
    // -------------------------------------------------------------------------
    // 召唤音咒实现部分
    // -------------------------------------------------------------------------
    
    function summonmonsters(gplayerimp, config, duration) {
        for (const creature of config.monsters) {
            for (let i = 0; i < creature.count; i++) {
                gplayerimp.creatureGenerator(creature.id, duration, 1);
            }
        }
    }
    
    class skill2008 extends SkillHook {
        constructor() {
            super(2008);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill2008;
            summonmonsters(gplayerimp, config, config.duration);
        }
    }

    class skill2009 extends SkillHook {
        constructor() {
            super(2009);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill2009;
            summonmonsters(gplayerimp, config, config.duration);
        }
    }

    class skill2010 extends SkillHook {
        constructor() {
            super(2010);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill2010;
            summonmonsters(gplayerimp, config, config.duration);
        }
    }

    class skill2385 extends SkillHook {
        constructor() {
            super(2385);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill2385;
            summonmonsters(gplayerimp, config, config.duration);
        }
    }
    
    const skillHooks = new Map();

    function registerSkillHook(skillId, hook) {
        skillHooks.set(skillId, hook);
    }

    function setupSkillInterceptor() {
        const findSkillFunctionAddress = (skillId) => {
            const len = String(skillId).length + 9;
            const symbol = `_ZNK4GNET${len}Skill${skillId}Stub11StateAttackEPNS_5SkillE`;
            return Module.findExportByName(null, symbol);
        };

        for (const [skillId, hook] of skillHooks.entries()) {
            const address = findSkillFunctionAddress(skillId);
            if (address) {
                Interceptor.replace(address, new NativeCallback((stub, skillPtr) => {
                    const skill = new Skill(skillPtr);
                    const playerPtr = skill.GetPlayer();
                    const player = new PlayerWrapper(playerPtr);
                    hook.StateAttack(player);
                }, 'void', ['pointer', 'pointer']));
            }
        }
    }

    function activateAllSkills() {
        const skillList = [
            new skill2008(),
            new skill2009(),
            new skill2010(),
            new skill2385()
        ];

        for (const skill of skillList) {
            registerSkillHook(skill.skillId, skill);
        }

        setupSkillInterceptor();
    }

    function loadFilterConfig() {
        const file = new File(VERSION_CONFIG.FILTER_CONFIG_PATH, "r");
        const content = file.readText();
        file.close();

        const config = { wordMap: {} };
        const lines = content.split('\n');
        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed || trimmed.startsWith('#')) continue;
            const parts = trimmed.split(',');
            const keyword = parts[0].trim();
            const replacement = parts[1].trim();
            config.wordMap[keyword] = replacement;
        }
        return config;
    }

    function filterMessage(msg) {
        const config = loadFilterConfig();

        if (/\d{8,}/.test(msg)) {
            return '请不要发布联系方式';
        }

        let result = msg;
        for (const keyword in config.wordMap) {
            result = result.replace(new RegExp(keyword, 'g'), config.wordMap[keyword]);
        }
        return result;
    }

    function setupChatFilter() {
        Interceptor.attach(ptr(VERSION_CONFIG.CHAT_FILTER_ADDRESS), {
            onEnter(args) {
                const msg = args[3];
                const msg_length = args[4].toInt32();
                const msgContent = msg.readUtf16String(msg_length / 2);
                const filtered = filterMessage(msgContent);

                const newMsgLen = filtered.length * 2;
                const newMsgBuf = Memory.alloc(newMsgLen + 2);
                newMsgBuf.writeUtf16String(filtered);

                args[3] = newMsgBuf;
                args[4] = ptr(newMsgLen);
            }
        });
    }

    loadConfig();
    activateAllSkills();
    setupChatFilter();

    })();
    