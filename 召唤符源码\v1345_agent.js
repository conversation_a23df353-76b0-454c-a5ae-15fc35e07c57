(function(){
    // ========================================================================
    // 版本适配配置 - 修改这里的地址和偏移值来适配不同版本
    // ========================================================================
    const VERSION_CONFIG = {
        // 召唤相关地址
        CREATE_MINORS_ADDRESS: "0x0813BD8C",        // object_interface::CreateMinors 函数地址  422:0x0813BD8C 1345:0x81c3b3c

        // 聊天过滤地址
        CHAT_FILTER_ADDRESS: "0x08059EE0",         // 聊天消息处理函数地址

        // 玩家对象偏移
        PLAYER_GPLAYERIMP_OFFSET: 0x4,             // PlayerWrapper -> GPlayerImp 偏移
        SKILL_PLAYER_OFFSET: 0x41,                 // Skill -> Player 偏移     422:0x41 1345:0x61  

        // 配置文件路径
        SUMMON_CONFIG_PATH: "/root/summon_config.txt",
        FILTER_CONFIG_PATH: "/root/filter_words.txt"
    };
    // ========================================================================

    class CPlusClass {
        constructor(pointer) {
            this.pointer = pointer;
        }
        getPointer() {
            return this.pointer;
        }
    }

    class GPlayerImp extends CPlusClass {
        static fromPlayerWrapper(playerWrapper) {
            const playerPtr = playerWrapper.getPointer();
            const gplayerImpPtr = playerPtr.add(VERSION_CONFIG.PLAYER_GPLAYERIMP_OFFSET).readPointer();
            return new GPlayerImp(gplayerImpPtr);
        }

        creatureGenerator(creatureId, duration, count, name) {
            const oi = Memory.alloc(0x08);
            oi.add(0).writePointer(this.pointer);
            oi.add(4).writeU32(0);

            // 根据反汇编代码，结构体大小是 0x48 字节
            const mp = Memory.alloc(0x48);

            // 清零整个结构体
            for (let i = 0; i < 0x48; i += 4) {
                mp.add(i).writeU32(0);
            }

            mp.add(0).writeU32(creatureId);      // mob_id (a3 + 0)
            mp.add(0x08).writeU32(duration);     // remain_time (a3 + 8)
            mp.add(0x14).writeFloat(1.0);        // exp_factor (a3 + 20)
            mp.add(0x18).writeFloat(1.0);        // money_scale (a3 + 24)
            mp.add(0x1c).writeFloat(1.0);        // drop_rate (a3 + 28)

            // 设置怪物名称，根据汇编代码分析
            if (name && name.length > 0) {
                // 尝试不同的编码方式
                // 方法1: 直接使用ASCII/ANSI编码
                const nameStr = name.substring(0, 17); // 留一个字节给结束符
                const nameLen = nameStr.length;

                // 写入名称长度到偏移 0x2B
                mp.add(0x2B).writeU8(nameLen + 1); // +1 包含结束符

                // 写入名称数据到偏移 0x2C，使用ANSI编码
                for (let i = 0; i < nameLen; i++) {
                    const charCode = nameStr.charCodeAt(i);
                    // 对于中文字符，使用简化处理
                    if (charCode > 127) {
                        mp.add(0x2C + i).writeU8(63); // '?' 字符
                    } else {
                        mp.add(0x2C + i).writeU8(charCode);
                    }
                }
                // 添加字符串结束符
                mp.add(0x2C + nameLen).writeU8(0);
            }

            const createMinorsFunc = new NativeFunction(
                ptr(VERSION_CONFIG.CREATE_MINORS_ADDRESS),
                "void",
                ["pointer", "pointer", "float"]
            );

            for (let i = 0; i < count; i++) {
                createMinorsFunc(oi, mp, 6.0);
            }
        }
    }

    class PlayerWrapper extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }
    }

    class Skill extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }
        GetPlayer() {
            return this.pointer.add(VERSION_CONFIG.SKILL_PLAYER_OFFSET).readPointer();
        }
    }

    class SkillHook {
        constructor(skillId) {
            this.skillId = skillId;
        }
    }
    
    function loadConfig() {
        const file = new File(VERSION_CONFIG.SUMMON_CONFIG_PATH, "r");
        const content = file.readText();
        file.close();
        const config = JSON.parse(content);
        global.summonConfig = config;
        console.log("[Summon] 配置加载成功，技能数量:", Object.keys(config).length);
        return config;
    }
    
    // -------------------------------------------------------------------------
    // 召唤音咒实现部分
    // -------------------------------------------------------------------------
    
    function summonmonsters(gplayerimp, config) {
        if (!config || !config.monsters || !Array.isArray(config.monsters)) {
            return;
        }

        for (const creature of config.monsters) {
            for (let i = 0; i < creature.count; i++) {
                gplayerimp.creatureGenerator(creature.id, creature.duration, 1, creature.name);
            }
        }
    }
    
    class skill2008 extends SkillHook {
        constructor() {
            super(2008);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig && global.summonConfig.skill2008;
            summonmonsters(gplayerimp, config);
        }
    }

    class skill2009 extends SkillHook {
        constructor() {
            super(2009);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig && global.summonConfig.skill2009;
            summonmonsters(gplayerimp, config);
        }
    }

    class skill2010 extends SkillHook {
        constructor() {
            super(2010);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig && global.summonConfig.skill2010;
            summonmonsters(gplayerimp, config);
        }
    }

    class skill2385 extends SkillHook {
        constructor() {
            super(2385);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig && global.summonConfig.skill2385;
            summonmonsters(gplayerimp, config);
        }
    }
    
    const skillHooks = new Map();

    function registerSkillHook(skillId, hook) {
        skillHooks.set(skillId, hook);
    }

    function setupSkillInterceptor() {
        const findSkillFunctionAddress = (skillId) => {
            const len = String(skillId).length + 9;
            const symbol = `_ZNK4GNET${len}Skill${skillId}Stub11StateAttackEPNS_5SkillE`;
            return Module.findExportByName(null, symbol);
        };

        for (const [skillId, hook] of skillHooks.entries()) {
            const address = findSkillFunctionAddress(skillId);
            if (address) {
                Interceptor.replace(address, new NativeCallback((stub, skillPtr) => {
                    const skill = new Skill(skillPtr);
                    const playerPtr = skill.GetPlayer();
                    const player = new PlayerWrapper(playerPtr);
                    hook.StateAttack(player);
                }, 'void', ['pointer', 'pointer']));
            }
        }
    }

    function activateAllSkills() {
        const skillList = [
            new skill2008(),
            new skill2009(),
            new skill2010(),
            new skill2385()
        ];

        for (const skill of skillList) {
            registerSkillHook(skill.skillId, skill);
        }

        setupSkillInterceptor();
    }

    function loadFilterConfig() {
        const file = new File(VERSION_CONFIG.FILTER_CONFIG_PATH, "r");
        const content = file.readText();
        file.close();

        const config = {
            wordMap: {},
            enabled: true,
            filterNumber: true,
            numberPattern: "\\d{8,}",
            numberReplacement: "请不要发布联系方式"
        };

        const lines = content.split('\n');
        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed || trimmed.startsWith('#')) continue;

            // 解析设置行
            if (trimmed.startsWith('设置:')) {
                const setting = trimmed.substring(3).trim();
                const parts = setting.split('=');
                if (parts.length >= 2) {
                    const key = parts[0].trim();
                    const value = parts[1].trim();

                    if (key === '启用过滤') {
                        config.enabled = (value.toLowerCase() === 'true');
                    } else if (key === '过滤数字') {
                        config.filterNumber = (value.toLowerCase() === 'true');
                    } else if (key === '数字规则') {
                        config.numberPattern = value;
                    } else if (key === '数字替换') {
                        config.numberReplacement = value;
                    }
                }
            }
            // 解析违禁词
            else {
                const parts = trimmed.split(',');
                if (parts.length >= 2) {
                    const keyword = parts[0].trim();
                    const replacement = parts[1].trim();
                    if (keyword && replacement) {
                        config.wordMap[keyword] = replacement;
                    }
                }
            }
        }
        return config;
    }

    // 全局配置缓存，避免每次都重新读取
    let filterConfigCache = null;

    function getFilterConfig() {
        if (!filterConfigCache) {
            filterConfigCache = loadFilterConfig();
        }
        return filterConfigCache;
    }

    function filterMessage(msg) {
        const config = getFilterConfig();

        // 检查是否启用过滤
        if (!config.enabled) {
            return msg;
        }

        // 检查数字过滤
        if (config.filterNumber && config.numberPattern) {
            const numberRegex = new RegExp(config.numberPattern);
            if (numberRegex.test(msg)) {
                return config.numberReplacement;
            }
        }

        // 检查违禁词
        let result = msg;
        for (const keyword in config.wordMap) {
            if (result.includes(keyword)) {
                result = result.replace(new RegExp(keyword, 'g'), config.wordMap[keyword]);
            }
        }
        return result;
    }

    function setupChatFilter() {
        Interceptor.attach(ptr(VERSION_CONFIG.CHAT_FILTER_ADDRESS), {
            onEnter(args) {
                this.args = args;
                const msg = args[3];
                const msg_length = args[4].toInt32();

                if (!msg || msg.isNull() || msg_length <= 0) return;

                const msgContent = msg.readUtf16String(msg_length / 2);

                if (msgContent && msgContent.length > 0) {
                    const filtered = filterMessage(msgContent);

                    if (filtered !== msgContent) {
                        // 分配新的内存并写入替换后的消息
                        const newMsgLen = filtered.length * 2;
                        const newMsgBuf = Memory.alloc(newMsgLen + 2);
                        newMsgBuf.writeUtf16String(filtered);

                        // 保存原始参数
                        this.originalMsg = {
                            ptr: args[3],
                            length: args[4]
                        };

                        // 更新参数
                        args[3] = newMsgBuf;
                        args[4] = ptr(newMsgLen);

                        this.isModified = true;
                        this.newMsgBuf = newMsgBuf;
                    }
                }
            },

            onLeave() {
                if (this.isModified && this.originalMsg) {
                    this.isModified = false;
                    this.originalMsg = null;
                    this.newMsgBuf = null;
                }
            }
        });
    }

    loadConfig();
    activateAllSkills();
    setupChatFilter();

    })();
    