(function(){
// 内联包含CPlusClass以避免依赖问题
class CPlusClass {
    constructor(pointer) {
        this.pointer = pointer;
    }

    ensurePointer() {
        if (!this.pointer || this.pointer.isNull()) {
            throw new Error("Invalid pointer");
        }
    }
    
    getPointer() {
        return this.pointer;
    }
}

// 通用获取原生函数功能
const functionMap = new Map();
const getNativeFunction = (symbolOrAddress, ret, args) => {
    if (functionMap.has(symbolOrAddress)) {
        return functionMap.get(symbolOrAddress);
    }

    let address;
    try {
        if (symbolOrAddress.startsWith('0x')) {
            address = ptr(symbolOrAddress);
        } else {
            address = Module.findExportByName(null, symbolOrAddress);
            if (!address) {
                return null;
            }
        }
        
        const f = new NativeFunction(address, ret, args);
        functionMap.set(symbolOrAddress, f);
        return f;
    } catch(e) {
        return null;
    }
};

// GPlayerImp 类实现
class GPlayerImp extends CPlusClass {
    static allPlayerInstances = new Map();

    static fromPlayerWrapper(playerWrapper) {
        try {
            const playerPtr = playerWrapper.getPointer();
            const gplayerImpPtr = playerPtr.add(0x4).readPointer();
            if (!gplayerImpPtr.isNull()) {
                return new GPlayerImp(gplayerImpPtr);
            }
            throw new Error("Could not get valid GPlayerImp pointer");
        } catch (error) {
            return null;
        }
    }

    creatureGenerator(creatureId, duration, count) {
        try {
            this.ensurePointer();
            
            const oi = Memory.alloc(0x08);
            oi.add(0).writePointer(this.pointer);
            oi.add(4).writeU32(0);
            const mp = Memory.alloc(0x48);
            mp.add(0).writeU32(creatureId);
            mp.add(0x08).writeU32(duration);
            mp.add(0x14).writeFloat(1.0);
            mp.add(0x18).writeFloat(1.0);
            mp.add(0x1c).writeFloat(1.0);

            const createMinorsFunc = new NativeFunction(
                ptr("0x81c3b3c"),
                "void",
                ["pointer", "pointer", "float"]
            );
            
            for (let i = 0; i < count; i++) {
                createMinorsFunc(oi, mp, 6.0);
            }
            return true;
        } catch (error) {
            return false;
        }
    }
}

// PlayerWrapper 类实现
class PlayerWrapper extends CPlusClass {
    constructor(pointer) {
        super(pointer);
    }
    
    static getCurrentPlayer() {
        try {
            const playerPtrAddress = ptr("0x091FE670");
            const playerPtr = Memory.readPointer(playerPtrAddress);
            
            if (playerPtr && !playerPtr.isNull()) {
                return new PlayerWrapper(playerPtr);
            }
            return null;
        } catch (error) {
            return null;
        }
    }
}

// Skill 类实现
class Skill extends CPlusClass {
    constructor(pointer) {
        super(pointer);
    }
    
    GetLevel() {
        return this.pointer.add(0x4).readInt();
    }
    
    GetPlayer() {
        return this.pointer.add(0x61).readPointer();
    }
}

// SkillHook 类实现
class SkillHook {
    skillId;
    calculateStates = [];

    constructor(skillId) {
        this.skillId = skillId;
    }

    get StateAttack() {
        return (player, skill) => {};
    }
}

// 配置文件读取功能
function loadConfig() {
    try {
        const configPaths = [
            "./summon_config_new.txt",
            "./summon_config.txt",
            "summon_config.txt",
            "./召唤符源码/summon_config.txt",
            "召唤符源码/summon_config.txt",
            "/root/summon_config.txt",
            "C:\\summon_config.txt",
            "summon_config.json"
        ];

        const defaultConfig = {
            skill2008: {
                monsters: [
                    { id: 22251, count: 2 },
                    { id: 22252, count: 3 },
                    { id: 22253, count: 1 }
                ],
                duration: 3600
            },
            skill2009: {
                monsters: [
                    { id: 22106, count: 1 },
                    { id: 22107, count: 2 }
                ],
                duration: 3600
            },
            skill2010: {
                monsters: [
                    { id: 13875, count: 1 }
                ],
                duration: 3600
            },
            skill3336: {
                monsters: [
                    { id: 133360, count: 1 }
                ],
                duration: 3600
            },
        };

        let config = defaultConfig;
        let configLoaded = false;

        for (const configFile of configPaths) {
            try {
                let content = null;
                try {
                    if (typeof File !== 'undefined') {
                        const file = new File(configFile, "r");
                        if (file) {
                            content = file.readText();
                            file.close();
                        }
                    }
                } catch (fileError) {
                    try {
                        if (typeof require !== 'undefined') {
                            const fs = require('fs');
                            if (fs.existsSync(configFile)) {
                                content = fs.readFileSync(configFile, 'utf8');
                            }
                        }
                    } catch (fsError) {
                        // 忽略
                    }

                    try {
                        if (typeof Java !== 'undefined') {
                            const Files = Java.use("java.nio.file.Files");
                            const Paths = Java.use("java.nio.file.Paths");
                            const path = Paths.get(configFile);
                            if (Files.exists(path)) {
                                const bytes = Files.readAllBytes(path);
                                content = String.fromCharCode.apply(null, bytes);
                            }
                        }
                    } catch (javaError) {
                        // 忽略
                    }
                }

                if (content && content.trim()) {
                    try {
                        const userConfig = JSON.parse(content);
                        for (const key in userConfig) {
                            if (defaultConfig.hasOwnProperty(key)) {
                                const skillConfig = userConfig[key];
                                if (skillConfig) {
                                    if (Array.isArray(skillConfig.monsters)) {
                                        config[key] = {
                                            monsters: skillConfig.monsters,
                                            duration: typeof skillConfig.duration === 'number' ? skillConfig.duration : 3600
                                        };
                                    }
                                    else if (Array.isArray(skillConfig.creatureIds)) {
                                        config[key] = {
                                            creatureIds: skillConfig.creatureIds,
                                            duration: typeof skillConfig.duration === 'number' ? skillConfig.duration : 3600,
                                            count: typeof skillConfig.count === 'number' ? skillConfig.count : 1
                                        };
                                    }
                                }
                            }
                        }
                        configLoaded = true;
                        break;
                    } catch (parseError) {
                        // 忽略
                    }
                }
            } catch (e) {
                // 忽略
            }
        }

        if (!configLoaded) {
            try {
                const exampleConfig = JSON.stringify(defaultConfig, null, 2);
                const examplePath = "./summon_config.txt.example";
                const exampleFile = new File(examplePath, "w");
                if (exampleFile) {
                    exampleFile.write(exampleConfig);
                    exampleFile.flush();
                    exampleFile.close();
                }
            } catch (e) {
                // 忽略
            }
        }

        global.summonConfig = config;
        return config;
    } catch (error) {
        const fallbackConfig = {
            skill2008: {
                monsters: [
                    { id: 22251, count: 2 },
                    { id: 22252, count: 3 },
                    { id: 22253, count: 1 }
                ],
                duration: 3600
            },
            skill2009: {
                monsters: [
                    { id: 22106, count: 1 },
                    { id: 22107, count: 2 }
                ],
                duration: 3600
            },
            skill2010: {
                monsters: [
                    { id: 13875, count: 1 }
                ],
                duration: 3600
            },
            skill3336: {
                monsters: [
                    { id: 133360, count: 1 }
                ],
                duration: 3600
            },
        };
        global.summonConfig = fallbackConfig;
        return fallbackConfig;
    }
}

// 通用函数：支持召唤单个或多个生物（兼容新旧配置格式）
function summonmonsters(gplayerimp, config, duration = 3600) {
    try {
        if (!gplayerimp || gplayerimp.pointer.isNull()) {
            return false;
        }

        // 检查配置格式
        if (config.monsters && Array.isArray(config.monsters)) {
            for (const creature of config.monsters) {
                if (typeof creature.id !== 'number' || creature.id <= 0) {
                    continue;
                }
                const count = typeof creature.count === 'number' ? creature.count : 1;
                for (let i = 0; i < count; i++) {
                    gplayerimp.creatureGenerator(creature.id, duration, 1);
                }
            }
        } else if (config.creatureIds && Array.isArray(config.creatureIds)) {
            const count = config.count || 1;
            for (const id of config.creatureIds) {
                if (typeof id !== 'number' || id <= 0) {
                    continue;
                }
                for (let i = 0; i < count; i++) {
                    gplayerimp.creatureGenerator(id, duration, 1);
                }
            }
        } else {
            return false;
        }
        return true;
    } catch (error) {
        return false;
    }
}

// 召唤音咒·背叛者
class skill2008 extends SkillHook {
    constructor() {
        super(2008);
    }

    StateAttack(player, skill) {
        try {
            if (!player || player.pointer.isNull()) {
                return;
            }

            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            if (!gplayerimp) {
                return;
            }

            const config = global.summonConfig?.skill2008 || {
                monsters: [
                    { id: 22251, count: 2 },
                    { id: 22252, count: 3 },
                    { id: 22253, count: 1 }
                ],
                duration: 3600
            };

            summonmonsters(gplayerimp, config, config.duration);
        } catch (error) {
            // 忽略错误
        }
    }
}

// 召唤音咒·邪恶先锋
class skill2009 extends SkillHook {
    constructor() {
        super(2009);
    }

    StateAttack(player, skill) {
        try {
            if (!player || player.pointer.isNull()) {
                return;
            }

            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            if (!gplayerimp) {
                return;
            }

            const config = global.summonConfig?.skill2009 || {
                monsters: [
                    { id: 22106, count: 1 },
                    { id: 22107, count: 2 }
                ],
                duration: 3600
            };

            summonmonsters(gplayerimp, config, config.duration);
        } catch (error) {
            // 忽略错误
        }
    }
}

// 召唤音咒·八荒火龙
class skill2010 extends SkillHook {
    constructor() {
        super(2010);
    }

    StateAttack(player, skill) {
        try {
            if (!player || player.pointer.isNull()) {
                return;
            }

            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            if (!gplayerimp) {
                return;
            }

            const config = global.summonConfig?.skill2010 || {
                monsters: [
                    { id: 13875, count: 1 }
                ],
                duration: 3600
            };

            summonmonsters(gplayerimp, config, config.duration);
        } catch (error) {
            // 忽略错误
        }
    }
}

// 召唤音咒·饕餮
class skill3336 extends SkillHook {
    constructor() {
        super(3336);
    }

    StateAttack(player, skill) {
        try {
            if (!player || player.pointer.isNull()) {
                return;
            }

            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            if (!gplayerimp) {
                return;
            }

            const config = global.summonConfig?.skill3336 || {
                monsters: [
                    { id: 133360, count: 1 }
                ],
                duration: 3600
            };

            summonmonsters(gplayerimp, config, config.duration);
        } catch (error) {
            // 忽略错误
        }
    }
}

// 简化版本的技能钩子系统
const skillHooks = new Map();

function registerSkillHook(skillId, hook) {
    skillHooks.set(skillId, hook);
}

function setupSkillInterceptor() {
    try {
        const findSkillFunctionAddress = (skillId) => {
            const len = String(skillId).length + 9;
            const symbol = `_ZNK4GNET${len}Skill${skillId}Stub11StateAttackEPNS_5SkillE`;
            return Module.findExportByName(null, symbol);
        };

        for (const [skillId, hook] of skillHooks.entries()) {
            const address = findSkillFunctionAddress(skillId);
            if (address) {
                const originalStateAttack = new NativeFunction(address, 'void', ['pointer', 'pointer']);

                Interceptor.replace(address, new NativeCallback((stub, skillPtr) => {
                    try {
                        if (!skillPtr.isNull()) {
                            const skill = new Skill(skillPtr);
                            const playerPtr = skill.GetPlayer();
                            if (playerPtr && !playerPtr.isNull()) {
                                const player = new PlayerWrapper(playerPtr);
                                hook.StateAttack(player, skill);
                            }
                        }
                    } catch (error) {
                        try {
                            originalStateAttack(stub, skillPtr);
                        } catch (e) {
                            // 忽略
                        }
                    }
                }, 'void', ['pointer', 'pointer']));
            }
        }

        return true;
    } catch (error) {
        return false;
    }
}

function setupGlobalApi() {
    try {
        global.summonApi = {
            summonmonsters: summonmonsters,
            reloadConfig: function() {
                return loadConfig();
            },
            showConfig: function() {
                return global.summonConfig;
            },
            summonById: function(creatureId, duration = 3600, count = 1) {
                const player = PlayerWrapper.getCurrentPlayer();
                if (player) {
                    const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                    if (gplayerimp) {
                        return gplayerimp.creatureGenerator(creatureId, duration, count);
                    }
                }
                return false;
            }
        };
        return true;
    } catch (error) {
        return false;
    }
}

function activateAllSkills() {
    try {
        const skillList = [
            new skill2008(),
            new skill2009(),
            new skill2010(),
            new skill3336(),
        ];

        for (const skill of skillList) {
            registerSkillHook(skill.skillId, skill);
        }

        setupSkillInterceptor();
        return true;
    } catch (error) {
        return false;
    }
}

// 启动插件
try {
    loadConfig();
    setupGlobalApi();
    activateAllSkills();

    Script.bindWeak(global, function() {
        if (global.summonApi) {
            delete global.summonApi;
        }
        if (global.summonConfig) {
            delete global.summonConfig;
        }
    });

} catch (error) {
    // 静默处理错误
}

})();
