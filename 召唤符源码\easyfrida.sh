#!/bin/bash

# 技能脚本路径 - 默认值
DEFAULT_SCRIPT_PATH="/root/_agent.js"
# 初始化脚本路径变量
SCRIPT_PATH="$DEFAULT_SCRIPT_PATH"

# 定义颜色代码
RED="\e[31m"
GREEN="\e[32m"
YELLOW="\e[33m"
BLUE="\e[34m"
CYAN="\e[36m"
RESET="\e[0m"

# 显示帅气的启动界面函数定义
show_banner() {
    clear
    echo -e "${BLUE}"
    echo "  ███████╗██████╗ ██╗██████╗  █████╗     ██╗███╗   ██╗     ██╗███████╗ ██████╗████████╗ ██████╗ ██████╗  "
    echo "  ██╔════╝██╔══██╗██║██╔══██╗██╔══██╗    ██║████╗  ██║     ██║██╔════╝██╔════╝╚══██╔══╝██╔═══██╗██╔══██╗ "
    echo "  █████╗  ██████╔╝██║██║  ██║███████║    ██║██╔██╗ ██║     ██║█████╗  ██║        ██║   ██║   ██║██████╔╝ "
    echo "  ██╔══╝  ██╔══██╗██║██║  ██║██╔══██║    ██║██║╚██╗██║██   ██║██╔══╝  ██║        ██║   ██║   ██║██╔══██╗ "
    echo "  ██║     ██║  ██║██║██████╔╝██║  ██║    ██║██║ ╚████║╚█████╔╝███████╗╚██████╗   ██║   ╚██████╔╝██║  ██║ "
    echo "  ╚═╝     ╚═╝  ╚═╝╚═╝╚═════╝ ╚═╝  ╚═╝    ╚═╝╚═╝  ╚═══╝ ╚════╝ ╚══════╝ ╚═════╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝ "
    echo -e "${CYAN}"
    echo "                                 422frida - 一键启动 - 多进程注入 - 支持定制                               "
    echo "                                                                                                      "
    echo "                                  当前时间: $(date '+%Y-%m-%d %H:%M:%S')                              "
    echo -e "${RESET}"
    echo "═════════════════════════════════════════════════════════════════════════════════════════════════════"
    echo ""
}

# 显示交互式菜单
show_menu() {
    show_banner
    
    # 显示gs进程
    GS_PIDS=($(ps -ef | grep "[g]s " | awk '{print $2}'))
    if [ ${#GS_PIDS[@]} -gt 0 ]; then
        echo -e "${GREEN}[系统信息] 找到 ${#GS_PIDS[@]} 个gs进程:${RESET}"
        for i in "${!GS_PIDS[@]}"; do
            CMDLINE=$(ps -p ${GS_PIDS[$i]} -o cmd= | cut -c 1-60)
            echo -e "${YELLOW}    $i) PID: ${GS_PIDS[$i]} - $CMDLINE${RESET}"
        done
        echo ""
    else
        echo -e "${RED}[系统信息] 未找到gs进程，请确保游戏已启动${RESET}"
        echo ""
    fi
    
    # 显示当前脚本信息
    if [ -f "$SCRIPT_PATH" ]; then
        echo -e "${GREEN}[脚本信息] 当前脚本: ${CYAN}$SCRIPT_PATH ${GREEN}($(du -h "$SCRIPT_PATH" | cut -f1))${RESET}"
    else
        echo -e "${RED}[脚本信息] 当前脚本: $SCRIPT_PATH ${RED}(文件不存在)${RESET}"
    fi
    
    # 显示已注入的进程信息
    INJECTED_COUNT=0
    for pid in "${GS_PIDS[@]}"; do
        if check_frida_injection "$pid"; then
            INJECTED_COUNT=$((INJECTED_COUNT+1))
        fi
    done
    
    if [ $INJECTED_COUNT -gt 0 ]; then
        echo -e "${GREEN}[注入状态] 已注入 ${INJECTED_COUNT}/${#GS_PIDS[@]} 个进程${RESET}"
    else
        echo -e "${YELLOW}[注入状态] 当前没有注入${RESET}"
    fi
    
    echo ""
    echo -e "${CYAN}请选择操作:${RESET}"
    echo -e "${CYAN}═════════════════════════════════════════════════════════════════${RESET}"
    echo -e " ${GREEN}1${RESET}) 注入全部gs进程"
    echo -e " ${GREEN}2${RESET}) 重新加载注入"
    echo -e " ${GREEN}3${RESET}) 停止所有注入"
    echo -e " ${GREEN}0${RESET}) 退出"
    echo -e "${CYAN}═════════════════════════════════════════════════════════════════${RESET}"
    echo -ne "${YELLOW}请输入选项 [0-3]:${RESET} "
    read choice
    
    case "$choice" in
        1)
            inject_to_all_processes
            ;;
        2)
            reload_injection
            ;;
        3)
            kill_frida_processes "false"
            ;;
        0)
            clear
            exit 0
            ;;
        *)
            ;;
    esac
    show_menu
}

# 显示使用帮助
show_help() {
    echo -e "${CYAN}使用方法:${RESET}"
    echo "  ./easyfrida.sh                 - 启动交互式菜单界面"
    echo "  ./easyfrida.sh -p              - 注入到所有gs进程"
    echo "  ./easyfrida.sh -k, --kill      - 停止所有已注入的frida进程"
    echo "  ./easyfrida.sh -r, --reload    - 快速重载所有注入(修改配置后使用)"
    echo "  ./easyfrida.sh -s, --script <脚本路径> - 使用指定脚本而非默认脚本"
    echo "  ./easyfrida.sh -h, --help      - 显示此帮助信息"
    echo ""
}



# 检查进程是否有frida注入的函数
check_frida_injection() {
    local pid=$1
    # 检查多种方式来确认注入状态
    local frida_ps_result=$(frida-ps 2>/dev/null | grep -w "$pid" | wc -l)
    local proc_maps_result=$(cat /proc/$pid/maps 2>/dev/null | grep -i frida | wc -l)
    local lsof_result=$(lsof -p $pid 2>/dev/null | grep -i frida | wc -l)

    if [ "$frida_ps_result" -gt 0 ] || [ "$proc_maps_result" -gt 0 ] || [ "$lsof_result" -gt 0 ]; then
        return 0  # 有注入
    else
        return 1  # 没有注入
    fi
}



# 停止所有已注入的frida进程
kill_frida_processes() {
    local is_cli="$1"

    echo "正在停止所有 frida 注入进程..."

    # 获取所有 frida 进程，但排除当前脚本进程
    CURRENT_PID=$$
    FRIDA_PIDS=$(ps aux | grep -E "frida.*-p.*-l" | grep -v grep | grep -v $CURRENT_PID | awk '{print $2}')

    if [ -n "$FRIDA_PIDS" ]; then
        echo "找到注入进程，正在终止..."
        for pid in $FRIDA_PIDS; do
            echo "终止进程 $pid"
            kill $pid 2>/dev/null
        done
        sleep 1
        # 强制终止仍在运行的进程
        for pid in $FRIDA_PIDS; do
            kill -9 $pid 2>/dev/null
        done
        echo "所有 frida 注入进程已停止"
    else
        echo "未找到活跃的 frida 注入进程"
    fi

    if [ "$is_cli" = "true" ]; then
        exit 0
    fi
}

# 执行注入到所有进程的操作
inject_to_all_processes() {
    if [ ! -f "$SCRIPT_PATH" ]; then
        echo "脚本文件不存在: $SCRIPT_PATH"
        return
    fi

    # 查找所有gs进程
    GS_PIDS=($(ps -ef | grep "[g]s " | awk '{print $2}'))

    if [ ${#GS_PIDS[@]} -eq 0 ]; then
        echo "未找到gs进程"
        return
    fi

    echo "找到 ${#GS_PIDS[@]} 个gs进程，开始注入..."

    # 注入到每个gs进程
    for i in "${!GS_PIDS[@]}"; do
        if kill -0 ${GS_PIDS[$i]} 2>/dev/null; then
            echo "注入进程 ${GS_PIDS[$i]}..."
            frida -p ${GS_PIDS[$i]} -l "$SCRIPT_PATH" --no-pause &
            sleep 0.5  # 给每个注入一点时间
        fi
    done

    echo "注入完成"
}

# 快速重载函数
reload_injection() {
    echo "正在停止所有 frida 注入进程..."

    # 获取所有 frida 进程，但排除当前脚本进程
    CURRENT_PID=$$
    FRIDA_PIDS=$(ps aux | grep -E "frida.*-p.*-l" | grep -v grep | grep -v $CURRENT_PID | awk '{print $2}')

    if [ -n "$FRIDA_PIDS" ]; then
        echo "找到注入进程，正在终止..."
        for pid in $FRIDA_PIDS; do
            echo "终止进程 $pid"
            kill $pid 2>/dev/null
        done
        sleep 1
        # 强制终止仍在运行的进程
        for pid in $FRIDA_PIDS; do
            kill -9 $pid 2>/dev/null
        done
    else
        echo "未找到活跃的 frida 注入进程"
    fi

    echo "等待进程清理完成..."
    sleep 1
    echo "重新注入所有进程..."
    inject_to_all_processes
}

# 解析命令行参数
RELOAD_MODE=false
INJECT_ALL=false

# 如果没有参数，显示交互式菜单
if [ $# -eq 0 ]; then
    show_menu
    exit 0
fi

# 有参数时，解析并执行对应操作
while [[ $# -gt 0 ]]; do
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -k|--kill)
            kill_frida_processes "true"
            exit 0
            ;;
        -r|--reload)
            RELOAD_MODE=true
            shift
            ;;
        -s|--script)
            if [[ -n "$2" && ! "$2" =~ ^- ]]; then
                SCRIPT_PATH="$2"
                shift 2
            else
                echo -e "${RED}[!] 错误: -s 选项需要一个脚本路径参数${RESET}"
                exit 1
            fi
            ;;
        -p)
            # -p参数表示注入所有gs进程，不需要具体进程号
            INJECT_ALL=true
            shift
            ;;
        *)
            echo -e "${RED}[!] 未知选项: $1${RESET}"
            show_help
            exit 1
            ;;
    esac
done

# 检查脚本文件是否存在
if [ ! -f "$SCRIPT_PATH" ]; then
    echo -e "${RED}[!] 错误: 脚本文件 '$SCRIPT_PATH' 不存在${RESET}"
    exit 1
fi

# 根据参数执行相应操作
if [ "$RELOAD_MODE" = true ]; then
    reload_injection
    exit 0
elif [ "$INJECT_ALL" = true ]; then
    # -p参数：注入所有gs进程
    inject_to_all_processes
    exit 0
else
    # 默认：注入到所有进程
    inject_to_all_processes
    exit 0
fi

