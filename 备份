#!/bin/bash

# 技能脚本路径 - 默认值
DEFAULT_SCRIPT_PATH="/root/v422_agent.js"
# 初始化脚本路径变量
SCRIPT_PATH="$DEFAULT_SCRIPT_PATH"

# 定义颜色代码
RED="\e[31m"
GREEN="\e[32m"
YELLOW="\e[33m"
BLUE="\e[34m"
CYAN="\e[36m"
RESET="\e[0m"

# 显示帅气的启动界面函数定义
show_banner() {
    clear
    echo -e "${BLUE}"
    echo "  ███████╗██████╗ ██╗██████╗  █████╗     ██╗███╗   ██╗     ██╗███████╗ ██████╗████████╗ ██████╗ ██████╗  "
    echo "  ██╔════╝██╔══██╗██║██╔══██╗██╔══██╗    ██║████╗  ██║     ██║██╔════╝██╔════╝╚══██╔══╝██╔═══██╗██╔══██╗ "
    echo "  █████╗  ██████╔╝██║██║  ██║███████║    ██║██╔██╗ ██║     ██║█████╗  ██║        ██║   ██║   ██║██████╔╝ "
    echo "  ██╔══╝  ██╔══██╗██║██║  ██║██╔══██║    ██║██║╚██╗██║██   ██║██╔══╝  ██║        ██║   ██║   ██║██╔══██╗ "
    echo "  ██║     ██║  ██║██║██████╔╝██║  ██║    ██║██║ ╚████║╚█████╔╝███████╗╚██████╗   ██║   ╚██████╔╝██║  ██║ "
    echo "  ╚═╝     ╚═╝  ╚═╝╚═╝╚═════╝ ╚═╝  ╚═╝    ╚═╝╚═╝  ╚═══╝ ╚════╝ ╚══════╝ ╚═════╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝ "
    echo -e "${CYAN}"
    echo "                          422frida - 一键启动 - 多进程注入 - 支持定制                                     "
    echo "                                                                                                      "
    echo "                                  当前时间: $(date '+%Y-%m-%d %H:%M:%S')                              "
    echo -e "${RESET}"
    echo "═════════════════════════════════════════════════════════════════════════════════════════════════════"
    echo ""
}

# 显示交互式菜单
show_menu() {
    show_banner
    
    # 显示gs进程
    GS_PIDS=($(ps -ef | grep "[g]s " | awk '{print $2}'))
    if [ ${#GS_PIDS[@]} -gt 0 ]; then
        echo -e "${GREEN}[系统信息] 找到 ${#GS_PIDS[@]} 个gs进程:${RESET}"
        for i in "${!GS_PIDS[@]}"; do
            CMDLINE=$(ps -p ${GS_PIDS[$i]} -o cmd= | cut -c 1-60)
            echo -e "${YELLOW}    $i) PID: ${GS_PIDS[$i]} - $CMDLINE${RESET}"
        done
        echo ""
    else
        echo -e "${RED}[系统信息] 未找到gs进程，请确保游戏已启动${RESET}"
        echo ""
    fi
    
    # 显示当前脚本信息
    if [ -f "$SCRIPT_PATH" ]; then
        echo -e "${GREEN}[脚本信息] 当前脚本: ${CYAN}$SCRIPT_PATH ${GREEN}($(du -h "$SCRIPT_PATH" | cut -f1))${RESET}"
    else
        echo -e "${RED}[脚本信息] 当前脚本: $SCRIPT_PATH ${RED}(文件不存在)${RESET}"
    fi
    
    # 显示已注入的进程信息
    INJECTED_COUNT=0
    for pid in "${GS_PIDS[@]}"; do
        if check_frida_injection "$pid"; then
            INJECTED_COUNT=$((INJECTED_COUNT+1))
        fi
    done
    
    if [ $INJECTED_COUNT -gt 0 ]; then
        echo -e "${GREEN}[注入状态] 已注入 ${INJECTED_COUNT}/${#GS_PIDS[@]} 个进程${RESET}"
    else
        echo -e "${YELLOW}[注入状态] 当前没有注入${RESET}"
    fi
    
    echo ""
    echo -e "${CYAN}请选择操作:${RESET}"
    echo -e "${CYAN}═════════════════════════════════════════════════════════════════${RESET}"
    echo -e " ${GREEN}1${RESET}) 注入全部gs进程"
    echo -e " ${GREEN}2${RESET}) 注入指定gs进程"
    echo -e " ${GREEN}3${RESET}) 重新加载注入"
    echo -e " ${GREEN}4${RESET}) 停止所有注入"
    echo -e " ${GREEN}5${RESET}) 切换注入脚本"
    echo -e " ${GREEN}6${RESET}) 查看当前脚本内容"
    echo -e " ${GREEN}0${RESET}) 退出"
    echo -e "${CYAN}═════════════════════════════════════════════════════════════════${RESET}"
    echo -ne "${YELLOW}请输入选项 [0-6]:${RESET} "
    read choice
    
    case "$choice" in
        1)
            inject_to_all_processes
            show_menu
            ;;
        2)
            inject_specific_process
            show_menu
            ;;
        3)
            reload_injection
            show_menu
            ;;
        4)
            kill_frida_processes
            echo -e "${GREEN}[✓] 已停止所有注入${RESET}"
            sleep 1
            show_menu
            ;;
        5)
            change_script
            show_menu
            ;;
        6)
            view_script_content
            show_menu
            ;;
        0)
            clear
            echo -e "${GREEN}感谢使用! 再见!${RESET}"
            exit 0
            ;;
        *)
            echo -e "${RED}[!] 无效选项，请重新输入${RESET}"
            sleep 1
            show_menu
            ;;
    esac
}

# 显示使用帮助
show_help() {
    echo -e "${CYAN}使用方法:${RESET}"
    echo "  ./easyfrida.sh                 - 启动交互式菜单界面"
    echo "  ./easyfrida.sh -p <进程编号>    - 注入到指定编号的gs进程"
    echo "  ./easyfrida.sh -k, --kill      - 停止所有已注入的frida进程"
    echo "  ./easyfrida.sh -r, --reload    - 快速重载所有注入(修改配置后使用)"
    echo "  ./easyfrida.sh -s, --script <脚本路径> - 使用指定脚本而非默认脚本"
    echo "  ./easyfrida.sh -h, --help      - 显示此帮助信息"
    echo ""
    echo -e "${YELLOW}注：以上选项可以组合使用，例如:${RESET}"
    echo "  ./easyfrida.sh -r -s /root/myscripts/custom.js  - 使用自定义脚本重载"
    echo "  ./easyfrida.sh -p 0 -s /root/myscripts/custom.js - 注入进程0使用自定义脚本"
    echo ""
}

# 查看脚本内容
view_script_content() {
    if [ ! -f "$SCRIPT_PATH" ]; then
        echo -e "${RED}[!] 错误: 脚本文件 '$SCRIPT_PATH' 不存在${RESET}"
        sleep 1
        return
    fi
    
    clear
    echo -e "${CYAN}当前脚本: $SCRIPT_PATH ($(du -h "$SCRIPT_PATH" | cut -f1))${RESET}"
    echo "═════════════════════════════════════════════════════════════════"
    
    # 确定使用哪种查看工具
    if command -v less &>/dev/null; then
        less -R "$SCRIPT_PATH"
    elif command -v more &>/dev/null; then
        more "$SCRIPT_PATH"
    else
        cat "$SCRIPT_PATH"
        echo ""
        echo -e "${YELLOW}按Enter键返回菜单...${RESET}"
        read
    fi
}

# 切换脚本函数
change_script() {
    clear
    echo -e "${CYAN}[切换脚本] 请选择:${RESET}"
    echo -e "${CYAN}═════════════════════════════════════════════════════════════════${RESET}"
    echo -e " ${GREEN}1${RESET}) 使用默认脚本 ($DEFAULT_SCRIPT_PATH)"
    echo -e " ${GREEN}2${RESET}) 使用根目录脚本 (/root/_agent.js)"
    echo -e " ${GREEN}3${RESET}) 手动输入脚本路径"
    echo -e " ${GREEN}0${RESET}) 返回上级菜单"
    echo -e "${CYAN}═════════════════════════════════════════════════════════════════${RESET}"
    echo -ne "${YELLOW}请输入选项 [0-3]:${RESET} "
    read script_choice
    
    case "$script_choice" in
        1)
            SCRIPT_PATH="$DEFAULT_SCRIPT_PATH"
            echo -e "${GREEN}[✓] 已切换到默认脚本${RESET}"
            sleep 1
            ;;
        2)
            SCRIPT_PATH="/root/_agent.js"
            if [ ! -f "$SCRIPT_PATH" ]; then
                echo -e "${RED}[!] 根目录脚本不存在: $SCRIPT_PATH${RESET}"
                sleep 2
            else
                echo -e "${GREEN}[✓] 已切换到根目录脚本: $SCRIPT_PATH${RESET}"
                sleep 1
            fi
            ;;
        3)
            echo -ne "${YELLOW}请输入脚本完整路径:${RESET} "
            read custom_path
            if [ -z "$custom_path" ]; then
                echo -e "${RED}[!] 路径不能为空${RESET}"
                sleep 1
            elif [ ! -f "$custom_path" ]; then
                echo -e "${RED}[!] 文件不存在: $custom_path${RESET}"
                sleep 1
            else
                SCRIPT_PATH="$custom_path"
                echo -e "${GREEN}[✓] 已切换到脚本: $SCRIPT_PATH${RESET}"
                sleep 1
            fi
            ;;
        0)
            return
            ;;
        *)
            echo -e "${RED}[!] 无效选项${RESET}"
            sleep 1
            ;;
    esac
}

# 检查进程是否有frida注入的函数
check_frida_injection() {
    local pid=$1
    local result=$(frida-ps | grep -w "$pid" | wc -l)
    if [ "$result" -gt 0 ]; then
        return 0  # 有注入
    else
        return 1  # 没有注入
    fi
}

# 注入指定进程函数
inject_specific_process() {
    clear
    # 获取GS进程列表
    GS_PIDS=($(ps -ef | grep "[g]s " | awk '{print $2}'))
    
    if [ ${#GS_PIDS[@]} -eq 0 ]; then
        echo -e "${RED}[!] 未找到gs进程${RESET}"
        sleep 1
        return
    fi
    
    # 显示进程列表
    echo -e "${CYAN}[*] 可用gs进程列表:${RESET}"
    for i in "${!GS_PIDS[@]}"; do
        CMDLINE=$(ps -p ${GS_PIDS[$i]} -o cmd= | cut -c 1-60)
        echo -e "${GREEN}    $i)${RESET} PID: ${GS_PIDS[$i]} - $CMDLINE"
    done
    
    # 用户选择
    echo -ne "${YELLOW}请选择要注入的进程编号 [0-$((${#GS_PIDS[@]}-1))] 或按q返回:${RESET} "
    read proc_choice
    
    if [[ "$proc_choice" == "q" || "$proc_choice" == "Q" ]]; then
        return
    fi
    
    if [[ ! "$proc_choice" =~ ^[0-9]+$ || "$proc_choice" -ge ${#GS_PIDS[@]} ]]; then
        echo -e "${RED}[!] 无效的进程索引${RESET}"
        sleep 1
        return
    fi
    
    # 检查脚本是否存在
    if [ ! -f "$SCRIPT_PATH" ]; then
        echo -e "${RED}[!] 错误: 脚本文件 '$SCRIPT_PATH' 不存在${RESET}"
        sleep 1
        return
    fi
    
    # 执行注入
    clear
    echo -e "${CYAN}[*] 正在注入进程 #$proc_choice (PID: ${GS_PIDS[$proc_choice]})...${RESET}"
    echo -e "${CYAN}[*] 使用脚本: $SCRIPT_PATH${RESET}"
    echo -e "${YELLOW}[提示] 注入完成后按Ctrl+C返回菜单${RESET}"
    echo ""
    
    # 使用frida注入
    frida -p ${GS_PIDS[$proc_choice]} -l "$SCRIPT_PATH"
    
    echo -e "${GREEN}[✓] 注入已完成或被终止${RESET}"
    sleep 1
}

# 退出所有已注入的frida进程
kill_frida_processes() {
    clear
    local mode="$1"
    local is_cli="$2"  # 新参数，判断是否为命令行模式调用
    
    echo -e "${YELLOW}[*] ${mode:+正在卸载}${mode:-正在停止所有}frida注入进程...${RESET}"
    
    # 保存gs进程ID列表，用于后续发送信号
    GS_PIDS=($(ps -ef | grep "[g]s " | awk '{print $2}'))
    FRIDA_COUNT=0
    UNLOAD_SUCCESS=0
    
    if [ ${#GS_PIDS[@]} -eq 0 ]; then
        echo -e "${YELLOW}[!] 未找到gs进程${RESET}"
    else
        # 根据模式选择不同处理方式
        if [ "$mode" = "reload" ]; then
            # 重载模式: 只卸载注入，不杀死进程
            for pid in "${GS_PIDS[@]}"; do
                # 检查是否有注入
                if check_frida_injection "$pid"; then
                    echo -e "${GREEN}[*] 从进程 $pid 卸载Frida注入...${RESET}"
                    frida -p $pid --unload &>/dev/null
                    sleep 0.2
                    
                    # 验证是否卸载成功
                    if ! check_frida_injection "$pid"; then
                        echo -e "${GREEN}    ✓ 卸载成功${RESET}"
                        UNLOAD_SUCCESS=$((UNLOAD_SUCCESS+1))
                    else
                        echo -e "${YELLOW}    ! 卸载可能不完全，将尝试继续${RESET}"
                    fi
                    
                    FRIDA_COUNT=$((FRIDA_COUNT+1))
                fi
            done
            
            # 如果是重载模式但没有成功卸载任何进程，可能没有注入或卸载失败
            if [ $FRIDA_COUNT -gt 0 ] && [ $UNLOAD_SUCCESS -eq 0 ]; then
                echo -e "${YELLOW}[!] 警告: 未能成功卸载任何注入。尝试强制清理...${RESET}"
                # 对于重载模式，如果温和卸载失败，尝试更强力的方法
                pkill -f frida-agent &>/dev/null
                sleep 0.3
            fi
            
        else
            # 停止模式: 完全清理
            # 1. 尝试找出所有frida相关进程
            FRIDA_PROCS=$(ps -ef | grep -E "frida|gum-js" | grep -v grep | awk '{print $2}')
            if [ ! -z "$FRIDA_PROCS" ]; then
                for pid in $FRIDA_PROCS; do
                    echo -e "${GREEN}[*] 终止frida相关进程: $pid${RESET}"
                    kill -9 $pid 2>/dev/null
                    FRIDA_COUNT=$((FRIDA_COUNT+1))
                done
            fi
            
            # 2. 通知所有gs进程卸载注入
            for pid in "${GS_PIDS[@]}"; do
                echo -e "${GREEN}[*] 向gs进程发送SIGUSR1信号用于卸载注入: $pid${RESET}"
                kill -SIGUSR1 $pid 2>/dev/null
                sleep 0.1
            done
            
            # 3. 直接使用frida命令卸载所有注入
            for pid in "${GS_PIDS[@]}"; do
                frida -p $pid --unload &>/dev/null
                sleep 0.1
            done
            
            # 4. 终止所有frida服务进程
            pkill -9 frida 2>/dev/null
            pkill -9 frida-server 2>/dev/null
            
            # 5. 检查/tmp目录下可能的frida临时文件并删除
            if [ -d /tmp/frida* ]; then
                rm -rf /tmp/frida* 2>/dev/null
                echo -e "${GREEN}[*] 已清理frida临时文件${RESET}"
            fi
            
            echo -e "${GREEN}[✓] 已尝试终止所有frida相关进程${RESET}"
            
            # 6. 建议用户重启游戏客户端以彻底清除注入
            echo -e "${YELLOW}[提示] 为确保彻底卸载注入，建议重启游戏客户端${RESET}"
            
            # 如果是命令行模式，打印一条额外信息
            if [ "$is_cli" = "true" ]; then
                echo -e "${GREEN}[✓] 所有注入已停止，可以继续使用脚本${RESET}"
                sleep 1
                # 如果是命令行模式，执行后显示菜单
                show_menu
                exit 0
            else
                sleep 1
            fi
        fi
    fi
    
    # 如果是重载模式，不要退出脚本
    if [ "$mode" != "reload" ]; then
        return 0
    fi
    
    return 0
}

# 执行注入到所有进程的操作
inject_to_all_processes() {
    clear
    # 检查脚本文件是否存在
    if [ ! -f "$SCRIPT_PATH" ]; then
        echo -e "${RED}[!] 错误: 脚本文件 '$SCRIPT_PATH' 不存在${RESET}"
        sleep 1
        return
    fi

    echo -e "${CYAN}[*] 使用脚本: $SCRIPT_PATH${RESET}"

    # 创建静默版本的脚本（用于所有进程）
    SILENT_SCRIPT="/tmp/silent_test1.js"
    cp "$SCRIPT_PATH" "$SILENT_SCRIPT"
    # 开头注释掉彩色输出部分
    sed -i '/.*\x1b\[/d' "$SILENT_SCRIPT"

    # 查找gs进程 - 更准确的匹配方式
    GS_PIDS=($(ps -ef | grep "[g]s " | awk '{print $2}'))

    if [ ${#GS_PIDS[@]} -eq 0 ]; then
        echo -e "${RED}[!] 未找到gs进程${RESET}"
        sleep 1
        return
    fi

    echo -e "${GREEN}[*] 找到 ${#GS_PIDS[@]} 个gs进程:${RESET}"
    for i in "${!GS_PIDS[@]}"; do
        CMDLINE=$(ps -p ${GS_PIDS[$i]} -o cmd= | cut -c 1-60)
        echo -e "${YELLOW}    $i) PID: ${GS_PIDS[$i]} - $CMDLINE${RESET}"
    done

    # 注入到所有找到的gs进程
    echo -e "${CYAN}[*] 开始注入到所有 ${#GS_PIDS[@]} 个gs进程${RESET}"
    
    # 先后台注入所有进程
    for i in "${!GS_PIDS[@]}"; do
        echo -e "${GREEN}[*] 后台注入进程 #$i (PID: ${GS_PIDS[$i]})...${RESET}"
        frida -p ${GS_PIDS[$i]} -l "$SILENT_SCRIPT" &>/dev/null &
        sleep 0.5  # 短暂等待确保注入开始
        echo -e "${GREEN}    ✓ 注入成功${RESET}"
    done
    
    # 然后显示成功信息
    echo -e "${GREEN}[✓] 完成所有注入任务，共注入 ${#GS_PIDS[@]} 个gs进程，使用脚本: $SCRIPT_PATH${RESET}"
    sleep 1

    # 清理临时脚本
    rm -f "$SILENT_SCRIPT"
}

# 快速重载函数
reload_injection() {
    clear
    echo -e "${CYAN}[*] 开始快速重载frida注入...${RESET}"
    echo -e "${YELLOW}[1/2] 正在温和卸载当前注入...${RESET}"
    
    # 先卸载当前所有注入，使用重载模式
    kill_frida_processes "reload"
    
    # 给应用一些时间来清理资源
    sleep 0.5
    
    echo -e "${YELLOW}[2/2] 正在重新注入...${RESET}"
    
    # 重新注入
    inject_to_all_processes
    
    echo -e "${GREEN}[✓] 重载完成！新的配置已经生效${RESET}"
    sleep 1
}

# 解析命令行参数
RELOAD_MODE=false
SPECIFIC_PROCESS=""

# 如果没有参数，显示交互式菜单
if [ $# -eq 0 ]; then
    show_menu
    exit 0
fi

# 有参数时，解析并执行对应操作
while [[ $# -gt 0 ]]; do
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -k|--kill)
            kill_frida_processes "" "true"  # 传递第二个参数表示这是命令行模式调用
            ;;
        -r|--reload)
            RELOAD_MODE=true
            shift
            ;;
        -s|--script)
            if [[ -n "$2" && ! "$2" =~ ^- ]]; then
                SCRIPT_PATH="$2"
                shift 2
            else
                echo -e "${RED}[!] 错误: -s 选项需要一个脚本路径参数${RESET}"
                exit 1
            fi
            ;;
        -p)
            if [[ -n "$2" && "$2" =~ ^[0-9]+$ ]]; then
                SPECIFIC_PROCESS="$2"
                shift 2
            else
                echo -e "${RED}[!] 错误: -p 选项需要一个数字参数${RESET}"
                exit 1
            fi
            ;;
        *)
            echo -e "${RED}[!] 未知选项: $1${RESET}"
            show_help
            exit 1
            ;;
    esac
done

# 检查脚本文件是否存在
if [ ! -f "$SCRIPT_PATH" ]; then
    echo -e "${RED}[!] 错误: 脚本文件 '$SCRIPT_PATH' 不存在${RESET}"
    exit 1
fi

# 根据参数执行相应操作
if [ "$RELOAD_MODE" = true ]; then
    reload_injection
    show_menu  # 重载后显示菜单
elif [ -n "$SPECIFIC_PROCESS" ]; then
    # 查找gs进程
    GS_PIDS=($(ps -ef | grep "[g]s " | awk '{print $2}'))
    
    if [ ${#GS_PIDS[@]} -eq 0 ]; then
        echo -e "${RED}[!] 未找到gs进程${RESET}"
        exit 1
    fi
    
    # 只注入指定的进程
    if [ "$SPECIFIC_PROCESS" -lt ${#GS_PIDS[@]} ]; then
        echo -e "${CYAN}[*] 使用脚本: $SCRIPT_PATH${RESET}"
        echo -e "${CYAN}[*] 正在注入进程 #$SPECIFIC_PROCESS (PID: ${GS_PIDS[$SPECIFIC_PROCESS]})${RESET}"
        frida -p ${GS_PIDS[$SPECIFIC_PROCESS]} -l "$SCRIPT_PATH"
        show_menu  # 注入后显示菜单
    else
        echo -e "${RED}[!] 无效的进程索引: $SPECIFIC_PROCESS${RESET}"
        exit 1
    fi
else
    # 注入到所有进程
    inject_to_all_processes
    show_menu  # 注入后显示菜单
fi