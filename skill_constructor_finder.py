import idaapi
import ida_<PERSON><PERSON>win
import idc
import ida_bytes
import ida_segment
import idautils

def bytes_to_pattern(skill_id, pattern_type="push"):
    """将技能ID转换为字节序列"""
    if not (0 <= skill_id <= 0xFFFF):
        return None, "技能ID必须在0到65535之间"

    low_byte = skill_id & 0xFF
    high_byte = (skill_id >> 8) & 0xFF

    if pattern_type == "push":
        # push 指令模式: 68 XX YY 00 00
        pattern_str = f"68 {low_byte:02X} {high_byte:02X} 00 00"
        pattern_bytes = bytes([0x68, low_byte, high_byte, 0x00, 0x00])
    else:  # mov模式
        # mov 指令模式: C7 46 04 XX YY 00 00
        pattern_str = f"C7 46 04 {low_byte:02X} {high_byte:02X} 00 00"
        pattern_bytes = bytes([0xC7, 0x46, 0x04, low_byte, high_byte, 0x00, 0x00])

    return (pattern_str, pattern_bytes), None

def find_skill_constructors(skill_id):
    """搜索技能构造函数"""
    all_matches = []

    # 只搜索.text段
    seg = ida_segment.get_segm_by_name(".text")
    if seg:
        segments_to_search = [(seg.start_ea, seg.end_ea, ".text")]
    else:
        # 如果找不到.text段，搜索所有段
        segments_to_search = []
        for seg_ea in idautils.Segments():
            seg = idaapi.getseg(seg_ea)
            if seg:
                segments_to_search.append((seg.start_ea, seg.end_ea, idc.get_segm_name(seg_ea)))

    if not segments_to_search:
        ida_kernwin.warning("错误: 没有找到可搜索的段")
        return []
    
    # 步骤1: 尝试push指令模式
    push_patterns, error = bytes_to_pattern(skill_id, "push")
    if error:
        ida_kernwin.warning(error)
        return []

    push_pattern_str, push_pattern_bytes = push_patterns

    for start_ea, end_ea, seg_name in segments_to_search:
        # 尝试IDA API搜索
        try:
            compiled_pattern = ida_bytes.parse_binpat_str(None, 0, push_pattern_str, 16)
            ea = start_ea
            while ea != idc.BADADDR:
                ea = ida_bytes.bin_search(ea, end_ea, compiled_pattern, 0, None)
                if ea == idc.BADADDR:
                    break
                func = idaapi.get_func(ea)
                if func:
                    func_start = func.start_ea
                    if func_start not in [addr for addr, _, _ in all_matches]:
                        all_matches.append((func_start, ea, "push指令模式"))
                ea += 1
        except Exception:
            # 如果IDA API失败，回退到手动搜索
            ea = start_ea
            while ea < end_ea - len(push_pattern_bytes):
                match = True
                for i, b in enumerate(push_pattern_bytes):
                    if ida_bytes.get_wide_byte(ea + i) != b:
                        match = False
                        break

                if match:
                    func = idaapi.get_func(ea)
                    if func:
                        func_start = func.start_ea
                        if func_start not in [addr for addr, _, _ in all_matches]:
                            all_matches.append((func_start, ea, "push指令模式"))

                ea += 1
    
    # 步骤2: 如果push模式没找到，尝试mov指令模式
    if not all_matches:
        mov_patterns, error = bytes_to_pattern(skill_id, "mov")
        if error:
            return []

        mov_pattern_str, mov_pattern_bytes = mov_patterns

        for start_ea, end_ea, seg_name in segments_to_search:
            # 尝试IDA API搜索
            try:
                compiled_pattern = ida_bytes.parse_binpat_str(None, 0, mov_pattern_str, 16)
                ea = start_ea
                while ea != idc.BADADDR:
                    ea = ida_bytes.bin_search(ea, end_ea, compiled_pattern, 0, None)
                    if ea == idc.BADADDR:
                        break
                    func = idaapi.get_func(ea)
                    if func:
                        func_start = func.start_ea
                        if func_start not in [addr for addr, _, _ in all_matches]:
                            all_matches.append((func_start, ea, "mov指令模式"))
                    ea += 1
            except Exception:
                # 如果IDA API失败，回退到手动搜索
                ea = start_ea
                while ea < end_ea - len(mov_pattern_bytes):
                    match = True
                    for i, b in enumerate(mov_pattern_bytes):
                        if ida_bytes.get_wide_byte(ea + i) != b:
                            match = False
                            break

                    if match:
                        func = idaapi.get_func(ea)
                        if func:
                            func_start = func.start_ea
                            if func_start not in [addr for addr, _, _ in all_matches]:
                                all_matches.append((func_start, ea, "mov指令模式"))

                    ea += 1
    
    return all_matches

def run_plugin():
    """手动运行插件的入口函数"""
    try:
        # 使用 ask_str 获取技能ID
        skill_id_str = ida_kernwin.ask_str("4677", 0, "请输入技能ID (0-65535):")
        if skill_id_str is None:
            return

        try:
            # 支持十六进制（0x前缀）和十进制输入
            if skill_id_str.lower().startswith("0x"):
                skill_id = int(skill_id_str, 16)
            else:
                skill_id = int(skill_id_str)

            if not (0 <= skill_id <= 0xFFFF):
                ida_kernwin.warning("技能ID必须在0到65535之间")
                return
        except ValueError:
            ida_kernwin.warning("请输入有效的数字")
            return

        matches = find_skill_constructors(skill_id)

        if not matches:
            ida_kernwin.warning(f"没有找到技能ID {skill_id} 的构造函数")
            return

        # 优先选择包含 "Stub" 的构造函数
        stub_matches = []
        for func_addr, insn_addr, pattern_type in matches:
            func_name = idc.get_func_name(func_addr) or ""
            if "Stub" in func_name:
                stub_matches.append((func_addr, insn_addr, pattern_type))

        # 如果找到 Stub 构造函数，优先使用
        if len(stub_matches) == 1:
            func_addr, insn_addr, pattern_type = stub_matches[0]
            idc.jumpto(func_addr)
            func_name = idc.get_func_name(func_addr) or "无名称"
            ida_kernwin.info(f"找到技能ID {skill_id} 的Stub构造函数\n函数名: {func_name}\n函数位置: 0x{func_addr:X}\n指令位置: 0x{insn_addr:X}\n匹配类型: {pattern_type}")
        elif len(stub_matches) > 1:
            # 如果有多个 Stub 构造函数，显示 Stub 列表让用户选择
            output = f"找到 {len(stub_matches)} 个技能ID {skill_id} 的Stub构造函数:\n"
            for i, (func_addr, insn_addr, pattern_type) in enumerate(stub_matches):
                func_name = idc.get_func_name(func_addr) or "无名称"
                output += f"[{i}] 0x{func_addr:X} ({func_name}) - 指令: 0x{insn_addr:X} ({pattern_type})\n"
            ida_kernwin.info(output)

            choice = ida_kernwin.ask_str("0", 0, f"找到多个Stub构造函数。请输入索引(0-{len(stub_matches)-1})跳转:")
            try:
                idx = int(choice)
                if 0 <= idx < len(stub_matches):
                    func_addr = stub_matches[idx][0]
                    idc.jumpto(func_addr)
                else:
                    ida_kernwin.warning("索引无效")
            except ValueError:
                ida_kernwin.warning("输入无效")
        elif len(matches) == 1:
            # 如果只有一个匹配结果，直接跳转
            func_addr, insn_addr, pattern_type = matches[0]
            idc.jumpto(func_addr)
            func_name = idc.get_func_name(func_addr) or "无名称"
            ida_kernwin.info(f"找到技能ID {skill_id} 的构造函数\n函数名: {func_name}\n函数位置: 0x{func_addr:X}\n指令位置: 0x{insn_addr:X}\n匹配类型: {pattern_type}")
        else:
            # 如果有多个结果，显示列表让用户选择
            output = f"找到 {len(matches)} 个技能ID {skill_id} 的匹配结果:\n"
            for i, (func_addr, insn_addr, pattern_type) in enumerate(matches):
                func_name = idc.get_func_name(func_addr) or "无名称"
                output += f"[{i}] 0x{func_addr:X} ({func_name}) - 指令: 0x{insn_addr:X} ({pattern_type})\n"
            ida_kernwin.info(output)

            choice = ida_kernwin.ask_str("0", 0, f"找到多个匹配结果。请输入索引(0-{len(matches)-1})跳转:")
            try:
                idx = int(choice)
                if 0 <= idx < len(matches):
                    func_addr = matches[idx][0]
                    idc.jumpto(func_addr)
                else:
                    ida_kernwin.warning("索引无效")
            except ValueError:
                ida_kernwin.warning("输入无效")
    except Exception as e:
        ida_kernwin.warning(f"插件错误: {str(e)}")

# 添加菜单项处理类
class SkillFinderMenuHandler(idaapi.action_handler_t):
    def __init__(self):
        idaapi.action_handler_t.__init__(self)
        
    def activate(self, ctx):
        run_plugin()
        return 1
        
    def update(self, ctx):
        return idaapi.AST_ENABLE_ALWAYS

class SkillConstructorFinder(idaapi.plugin_t):
    flags = idaapi.PLUGIN_KEEP  # 确保插件常驻
    comment = "通过技能ID查找技能构造函数"
    help = "输入技能ID定位其构造函数"
    wanted_name = "技能构造函数查找器"
    wanted_hotkey = "SHIFT-Z"
    
    # 菜单操作名称和描述
    menu_name = "查找技能构造函数"
    menu_path = "Edit/Plugins/"

    def init(self):
        # 注册菜单项
        action_desc = idaapi.action_desc_t(
            'skill_finder:run',    # 唯一标识符
            self.menu_name,        # 菜单项显示名称
            SkillFinderMenuHandler(), # 处理类实例
            self.wanted_hotkey,    # 快捷键
            self.comment           # 工具提示
        )

        if idaapi.register_action(action_desc):
            idaapi.attach_action_to_menu(
                self.menu_path + self.menu_name,  # 菜单路径
                'skill_finder:run',               # 动作名称
                0                                 # 标志
            )

        return idaapi.PLUGIN_OK

    def run(self, arg):
        run_plugin()

    def term(self):
        # 卸载时移除菜单项
        idaapi.unregister_action('skill_finder:run')

def PLUGIN_ENTRY():
    return SkillConstructorFinder()