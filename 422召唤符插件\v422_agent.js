(function(c,d){const P=b,e=c();while(!![]){try{const f=-parseInt(P(0xe4))/0x1*(parseInt(P(0xde))/0x2)+parseInt(P(0x102))/0x3+parseInt(P(0xfc))/0x4+parseInt(P(0x106))/0x5+parseInt(P(0xf4))/0x6+-parseInt(P(0x101))/0x7+-parseInt(P(0x104))/0x8;if(f===d)break;else e['push'](e['shift']());}catch(g){e['push'](e['shift']());}}}(a,0x848a8),((()=>{const aj=b;var c={'../_agent.js':()=>{eval('(function () {\n    const Q = b;\n    class e {\n        constructor(C) {\n            this[\'pointer\'] = C;\n        }\n        [Q(239)]() {\n            const R = Q;\n            if (!this[\'pointer\'] || this[R(247)][R(236)]())\n                throw new Error(\'Invalid\\\\x20pointer\');\n        }\n        [Q(268)]() {\n            return this[\'pointer\'];\n        }\n    }\n    class f {\n        [\'originalCalculate\'];\n        [\'originalStub\'];\n        [Q(227)](C, D, E) {\n            const S = Q;\n            this[\'originalCalculate\'] && this[S(220)] && this[\'originalCalculate\'](this[S(220)], D[\'getPointer\']());\n        }\n    }\n    class g {\n        [Q(221)];\n        [\'originalStub\'];\n        [Q(223)](C, D, E) {\n            const T = Q;\n            this[\'originalStateAttack\'] && this[\'originalStub\'] && this[T(221)](this[\'originalStub\'], D[T(268)]());\n        }\n    }\n    class h {\n        [Q(241)];\n        [Q(220)];\n        [Q(218)](C, D, E) {\n            const U = Q;\n            this[U(241)] && this[\'originalStub\'] && this[\'originalBlessMe\'](this[U(220)], D[U(268)]());\n        }\n    }\n    const i = new Map(), j = (C, D, E) => {\n            const V = Q;\n            if (i[V(264)](C))\n                return i[\'get\'](C);\n            let F;\n            try {\n                if (C[\'startsWith\'](\'0x\'))\n                    F = ptr(C);\n                else {\n                    F = Module[V(255)](null, C);\n                    if (!F)\n                        return null;\n                }\n                const G = new NativeFunction(F, D, E);\n                return i[V(266)](C, G), G;\n            } catch (H) {\n                return null;\n            }\n        };\n    class k extends e {\n        static [\'allPlayerInstances\'] = new Map();\n        static [Q(232)](C) {\n            const W = Q;\n            try {\n                const D = C[W(268)](), E = D[\'add\'](4)[\'readPointer\']();\n                if (!E[W(236)]())\n                    return new k(E);\n                throw new Error(\'Could\\\\x20not\\\\x20get\\\\x20valid\\\\x20GPlayerImp\\\\x20pointer\');\n            } catch (F) {\n                return null;\n            }\n        }\n        [\'creatureGenerator\'](C, D, E) {\n            const X = Q;\n            try {\n                this[X(239)]();\n                const F = Memory[X(226)](8);\n                F[X(237)](0)[\'writePointer\'](this[\'pointer\']), F[X(237)](4)[\'writeU32\'](0);\n                const G = Memory[X(226)](72);\n                G[\'add\'](0)[\'writeU32\'](C), G[\'add\'](8)[X(248)](D), G[\'add\'](20)[X(254)](1), G[X(237)](24)[X(254)](1), G[X(237)](28)[X(254)](1);\n                const H = new NativeFunction(ptr(X(245)), \'void\', [\n                    \'pointer\',\n                    X(247),\n                    X(249)\n                ]);\n                for (let I = 0; I < E; I++) {\n                    H(F, G, 6);\n                }\n                return !![];\n            } catch (J) {\n                return ![];\n            }\n        }\n    }\n    class l extends e {\n        constructor(C) {\n            super(C);\n        }\n        static [\'getCurrentPlayer\']() {\n            const Y = Q;\n            try {\n                const C = ptr(Y(251)), D = Memory[Y(265)](C);\n                if (D && !D[Y(236)]())\n                    return new l(D);\n                return null;\n            } catch (E) {\n                return null;\n            }\n        }\n    }\n    class m extends e {\n        constructor(C) {\n            super(C);\n        }\n        [\'GetLevel\']() {\n            const Z = Q;\n            return this[Z(247)][\'add\'](4)[\'readInt\']();\n        }\n        [Q(234)]() {\n            const a0 = Q;\n            return this[a0(247)][a0(237)](65)[a0(265)]();\n        }\n    }\n    class n {\n        constructor(C, D) {\n            const a1 = Q;\n            this[a1(216)] = C, this[a1(238)] = D;\n        }\n        static get [\'TYPES\']() {\n            const a2 = Q;\n            return {\n                \'VALUE\': \'value\',\n                \'RATIO\': \'ratio\',\n                \'MULTIPLY\': a2(243)\n            };\n        }\n    }\n    class o {\n        constructor(C) {\n            this[\'skillId\'] = C;\n        }\n    }\n    class p {\n        [\'skillId\'];\n        [\'calculateStates\'] = [];\n        constructor(C) {\n            this[\'skillId\'] = C;\n        }\n        get [Q(223)]() {\n            return (C, D) => {\n            };\n        }\n    }\n    function q() {\n        const a3 = Q;\n        try {\n            const C = a3(230), D = {\n                    \'skill370\': {\n                        \'creatureIds\': [\n                            22251,\n                            22252,\n                            22253\n                        ],\n                        \'duration\': 3600,\n                        \'count\': 1\n                    },\n                    \'skill371\': {\n                        \'creatureIds\': [\n                            22106,\n                            22107\n                        ],\n                        \'duration\': 3600,\n                        \'count\': 1\n                    },\n                    \'skill372\': {\n                        \'creatureIds\': [13875],\n                        \'duration\': 3600,\n                        \'count\': 1\n                    },\n                    \'skill373\': {\n                        \'creatureIds\': [13730],\n                        \'duration\': 3600,\n                        \'count\': 1\n                    },\n                    \'skill374\': {\n                        \'creatureIds\': [13581],\n                        \'duration\': 3600,\n                        \'count\': 1\n                    }\n                };\n            let E = D;\n            try {\n                const F = new File(C, \'r\');\n                if (F) {\n                    const G = F[a3(240)]();\n                    F[a3(253)]();\n                    if (G && G[a3(259)]()) {\n                        const H = JSON[a3(242)](G);\n                        for (const I in H) {\n                            if (D[\'hasOwnProperty\'](I)) {\n                                const J = H[I];\n                                J && Array[\'isArray\'](J[a3(231)]) && (E[I] = {\n                                    \'creatureIds\': J[\'creatureIds\'],\n                                    \'duration\': typeof J[a3(256)] === a3(233) ? J[\'duration\'] : 3600,\n                                    \'count\': typeof J[a3(215)] === a3(233) ? J[a3(215)] : 1\n                                });\n                            }\n                        }\n                    }\n                }\n            } catch (K) {\n                try {\n                    const L = JSON[\'stringify\'](D, null, 2), M = new File(C + \'.example\', \'w\');\n                    M[\'write\'](L), M[a3(267)](), M[\'close\']();\n                } catch (N) {\n                }\n            }\n            return global[\'summonConfig\'] = E, E;\n        } catch (O) {\n            return {\n                \'skill370\': {\n                    \'creatureIds\': [\n                        22251,\n                        22252,\n                        22253\n                    ],\n                    \'duration\': 3600,\n                    \'count\': 1\n                },\n                \'skill371\': {\n                    \'creatureIds\': [\n                        22106,\n                        22107\n                    ],\n                    \'duration\': 3600,\n                    \'count\': 1\n                },\n                \'skill372\': {\n                    \'creatureIds\': [13875],\n                    \'duration\': 3600,\n                    \'count\': 1\n                },\n                \'skill373\': {\n                    \'creatureIds\': [13730],\n                    \'duration\': 3600,\n                    \'count\': 1\n                },\n                \'skill374\': {\n                    \'creatureIds\': [13581],\n                    \'duration\': 3600,\n                    \'count\': 1\n                }\n            };\n        }\n    }\n    function r(C, D, E = 3600, F = 1) {\n        const a4 = Q;\n        try {\n            if (!C || C[\'pointer\'][a4(236)]())\n                return ![];\n            if (Array[a4(217)](D))\n                for (const G of D) {\n                    if (typeof G !== a4(233) || G <= 0)\n                        continue;\n                    for (let H = 0; H < F; H++) {\n                        C[a4(229)](G, E, 1);\n                    }\n                }\n            else {\n                if (typeof D !== a4(233) || D <= 0)\n                    return ![];\n                for (let I = 0; I < F; I++) {\n                    C[a4(229)](D, E, 1);\n                }\n            }\n            return !![];\n        } catch (J) {\n            return ![];\n        }\n    }\n    class s extends p {\n        constructor() {\n            super(370);\n        }\n        [\'StateAttack\'](C, D) {\n            const a5 = Q;\n            try {\n                if (!C || C[\'pointer\'][a5(236)]())\n                    return;\n                const E = k[\'fromPlayerWrapper\'](C);\n                if (!E)\n                    return;\n                const F = global[\'summonConfig\']?.[\'skill370\'] || {\n                    \'creatureIds\': [\n                        22251,\n                        22252,\n                        22253\n                    ],\n                    \'duration\': 3600,\n                    \'count\': 1\n                };\n                r(E, F[\'creatureIds\'], F[a5(256)], F[a5(215)]);\n            } catch (G) {\n            }\n        }\n    }\n    class t extends p {\n        constructor() {\n            super(371);\n        }\n        [Q(223)](C, D) {\n            const a6 = Q;\n            try {\n                if (!C || C[a6(247)][a6(236)]())\n                    return;\n                const E = k[\'fromPlayerWrapper\'](C);\n                if (!E)\n                    return;\n                const F = global[\'summonConfig\']?.[a6(225)] || {\n                    \'creatureIds\': [\n                        22106,\n                        22107\n                    ],\n                    \'duration\': 3600,\n                    \'count\': 1\n                };\n                r(E, F[\'creatureIds\'], F[\'duration\'], F[a6(215)]);\n            } catch (G) {\n            }\n        }\n    }\n    class u extends p {\n        constructor() {\n            super(372);\n        }\n        [Q(223)](C, D) {\n            const a7 = Q;\n            try {\n                if (!C || C[a7(247)][a7(236)]())\n                    return;\n                const E = k[a7(232)](C);\n                if (!E)\n                    return;\n                const F = global[a7(250)]?.[\'skill372\'] || {\n                    \'creatureIds\': [13875],\n                    \'duration\': 3600,\n                    \'count\': 1\n                };\n                r(E, F[a7(231)], F[a7(256)], F[a7(215)]);\n            } catch (G) {\n            }\n        }\n    }\n    class v extends p {\n        constructor() {\n            super(373);\n        }\n        [Q(223)](C, D) {\n            const a8 = Q;\n            try {\n                if (!C || C[\'pointer\'][a8(236)]())\n                    return;\n                const E = k[a8(232)](C);\n                if (!E)\n                    return;\n                const F = global[\'summonConfig\']?.[a8(235)] || {\n                    \'creatureIds\': [13730],\n                    \'duration\': 3600,\n                    \'count\': 1\n                };\n                r(E, F[a8(231)], F[\'duration\'], F[\'count\']);\n            } catch (G) {\n            }\n        }\n    }\n    class w extends p {\n        constructor() {\n            super(374);\n        }\n        [\'StateAttack\'](C, D) {\n            const a9 = Q;\n            try {\n                if (!C || C[\'pointer\'][\'isNull\']())\n                    return;\n                const E = k[\'fromPlayerWrapper\'](C);\n                if (!E)\n                    return;\n                const F = global[a9(250)]?.[\'skill374\'] || {\n                    \'creatureIds\': [13581],\n                    \'duration\': 3600,\n                    \'count\': 1\n                };\n                r(E, F[\'creatureIds\'], F[\'duration\'], F[\'count\']);\n            } catch (G) {\n            }\n        }\n    }\n    const x = new Map();\n    function y(C, D) {\n        x[\'set\'](C, D);\n    }\n    function z() {\n        const ab = Q;\n        try {\n            const C = D => {\n                const aa = b, E = String(D)[aa(246)] + 9, F = \'_ZNK4GNET\' + E + \'Skill\' + D + aa(219);\n                return Module[\'findExportByName\'](null, F);\n            };\n            for (const [D, E] of x[ab(261)]()) {\n                const F = C(D);\n                if (F) {\n                    const G = new NativeFunction(F, ab(224), [\n                        ab(247),\n                        ab(247)\n                    ]);\n                    Interceptor[\'replace\'](F, new NativeCallback((H, I) => {\n                        const ac = ab;\n                        try {\n                            if (!I[\'isNull\']()) {\n                                const J = new m(I), K = J[\'GetPlayer\']();\n                                if (K && !K[ac(236)]()) {\n                                    const L = new l(K), M = k[ac(232)](L);\n                                    E[\'StateAttack\'](L, J);\n                                }\n                            }\n                        } catch (N) {\n                            try {\n                                G(H, I);\n                            } catch (O) {\n                            }\n                        }\n                    }, \'void\', [\n                        ab(247),\n                        \'pointer\'\n                    ]));\n                }\n            }\n            return !![];\n        } catch (H) {\n            return ![];\n        }\n    }\n    function A() {\n        try {\n            return global[\'summonApi\'] = {\n                \'summonCreatures\': r,\n                \'summonBackstabber\': function () {\n                    const ad = b, C = l[\'getCurrentPlayer\']();\n                    if (C) {\n                        const D = k[ad(232)](C);\n                        if (D)\n                            return r(D, [\n                                22251,\n                                22252,\n                                22253\n                            ]);\n                    }\n                    return ![];\n                },\n                \'summonEvilVanguard\': function () {\n                    const ae = b, C = l[\'getCurrentPlayer\']();\n                    if (C) {\n                        const D = k[ae(232)](C);\n                        if (D)\n                            return r(D, [\n                                22106,\n                                22107\n                            ]);\n                    }\n                    return ![];\n                },\n                \'summonDragon\': function () {\n                    const af = b, C = l[af(263)]();\n                    if (C) {\n                        const D = k[\'fromPlayerWrapper\'](C);\n                        if (D)\n                            return r(D, 13875);\n                    }\n                    return ![];\n                },\n                \'summonTaotie\': function () {\n                    const ag = b, C = l[ag(263)]();\n                    if (C) {\n                        const D = k[ag(232)](C);\n                        if (D)\n                            return r(D, 13730);\n                    }\n                    return ![];\n                },\n                \'summonBeastGod\': function () {\n                    const ah = b, C = l[ah(263)]();\n                    if (C) {\n                        const D = k[\'fromPlayerWrapper\'](C);\n                        if (D)\n                            return r(D, 13581);\n                    }\n                    return ![];\n                },\n                \'summonById\': function (C, D = 3600, E = 1) {\n                    const ai = b, F = l[ai(263)]();\n                    if (F) {\n                        const G = k[\'fromPlayerWrapper\'](F);\n                        if (G)\n                            return G[ai(229)](C, D, E);\n                    }\n                    return ![];\n                }\n            }, !![];\n        } catch (C) {\n            return ![];\n        }\n    }\n    function B() {\n        try {\n            const C = [\n                new s(),\n                new t(),\n                new u(),\n                new v(),\n                new w()\n            ];\n            for (const D of C) {\n                y(D[\'skillId\'], D);\n            }\n            return z(), !![];\n        } catch (E) {\n            return ![];\n        }\n    }\n    try {\n        q(), A(), B();\n    } catch (C) {\n    }\n}());');}},d={};c[aj(0xd6)]();})()));function b(c,d){const e=a();return b=function(f,g){f=f-0xd6;let h=e[f];return h;},b(c,d);}function a(){const ak=['readPointer','set','flush','getPointer','../_agent.js','count','type','isArray','BlessMe','Stub11StateAttackEPNS_5SkillE','originalStub','originalStateAttack','4yJcnen','StateAttack','void','skill371','alloc','Calculate','382018qlcFSq','creatureGenerator','/root/summon_config.txt','creatureIds','fromPlayerWrapper','number','GetPlayer','skill373','isNull','add','value','ensurePointer','readText','originalBlessMe','parse','multiply','5226150JVuKsZ','0x0813BD8C','length','pointer','writeU32','float','summonConfig','0x091FE670','2220652rSWULp','close','writeFloat','findExportByName','duration','2970016zpzgJM','860787pIvFzJ','trim','2741520acOurj','entries','1803925YMqqTu','getCurrentPlayer','has'];a=function(){return ak;};return a();}