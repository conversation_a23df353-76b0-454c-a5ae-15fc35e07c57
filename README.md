# 聊天过滤功能

## 📁 文件
- `v1345_agent.js` - 主脚本
- `chat_filter.txt` - 配置文件

## 🚀 使用

### 1. 编辑配置文件 `chat_filter.txt`
```
# 违禁词（完全过滤）
外挂
代练
金币
QQ
群号

# 词汇替换（只替换词汇）
草->艹
操->艹
666->厉害
牛逼->牛批
```

### 2. 加载脚本
```bash
frida -U -l v1345_agent.js -f 游戏包名
```

### 3. 重新加载配置（修改txt文件后）
```javascript
summonApi.chatFilter.reloadConfig();
```

## 🎯 效果
- **违禁词**: "有外挂吗" → "系统消息已过滤"
- **词汇替换**: "你这个草包真666" → "你这个艹包真厉害"

## 📝 常用命令
```javascript
// 查看配置
summonApi.chatFilter.showConfig();

// 测试过滤
summonApi.chatFilter.testFilter("外挂");

// 测试替换
summonApi.chatFilter.testWordReplacement("草包");

// 重新加载
summonApi.chatFilter.reloadConfig();
```

完毕！
