(function(){
    class CPlusClass {
        constructor(pointer) {
            this.pointer = pointer;
        }

        getPointer() {
            return this.pointer;
        }
    }

    class GPlayerImp extends CPlusClass {
        static fromPlayerWrapper(playerWrapper) {
            const playerPtr = playerWrapper.getPointer();
            const gplayerImpPtr = playerPtr.add(0x4).readPointer();
            return new GPlayerImp(gplayerImpPtr);
        }

        creatureGenerator(creatureId, duration, count) {
            const oi = Memory.alloc(0x08);
            oi.add(0).writePointer(this.pointer);
            oi.add(4).writeU32(0);
            const mp = Memory.alloc(0x48);
            mp.add(0).writeU32(creatureId);
            mp.add(0x08).writeU32(duration);
            mp.add(0x14).writeFloat(1.0);
            mp.add(0x18).writeFloat(1.0);
            mp.add(0x1c).writeFloat(1.0);

            const createMinorsFunc = new NativeFunction(
                ptr("0x0813BD8C"),
                "void",
                ["pointer", "pointer", "float"]
            );

            for (let i = 0; i < count; i++) {
                createMinorsFunc(oi, mp, 6.0);
            }
        }
    }

    class PlayerWrapper extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }
    }

    class Skill extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }

        GetPlayer() {
            return this.pointer.add(0x41).readPointer();
        }
    }

    class SkillHook {
        constructor(skillId) {
            this.skillId = skillId;
        }
    }
    
    function loadConfig() {
        const defaultConfig = {
            skill370: { monsters: [{ id: 7, count: 1 }], duration: 3600 },
            skill371: { monsters: [{ id: 7, count: 1 }], duration: 3600 },
            skill372: { monsters: [{ id: 7, count: 1 }], duration: 3600 },
            skill373: { monsters: [{ id: 7, count: 1 }], duration: 3600 }
        };

        const file = new File("/root/summon_config.txt", "r");
        const content = file.readText();
        file.close();
        const config = JSON.parse(content);

        // 确保每个技能都有配置
        for (const skillKey in defaultConfig) {
            if (!config[skillKey]) {
                config[skillKey] = defaultConfig[skillKey];
            }
        }

        global.summonConfig = config;
        console.log("配置加载成功:", JSON.stringify(config, null, 2));
        return config;
    }
    
    // -------------------------------------------------------------------------
    // 召唤音咒实现部分
    // -------------------------------------------------------------------------
    
    function summonmonsters(gplayerimp, config, duration) {
        console.log("召唤函数调用，配置:", JSON.stringify(config, null, 2));

        if (!config || !config.monsters || !Array.isArray(config.monsters)) {
            console.log("配置错误: monsters 不存在或不是数组");
            return;
        }

        for (const creature of config.monsters) {
            console.log("召唤生物:", creature);
            for (let i = 0; i < creature.count; i++) {
                gplayerimp.creatureGenerator(creature.id, duration, 1);
            }
        }
    }
    
    class skill370 extends SkillHook {
        constructor() {
            super(370);
        }

        StateAttack(player) {
            console.log("技能370触发");
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill370;
            console.log("skill370配置:", config);
            if (config && config.duration) {
                summonmonsters(gplayerimp, config, config.duration);
            }
        }
    }

    class skill371 extends SkillHook {
        constructor() {
            super(371);
        }

        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill371;
            if (config && config.duration) {
                summonmonsters(gplayerimp, config, config.duration);
            }
        }
    }

    class skill372 extends SkillHook {
        constructor() {
            super(372);
        }

        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill372;
            summonmonsters(gplayerimp, config, config.duration);
        }
    }

    class skill373 extends SkillHook {
        constructor() {
            super(373);
        }
        StateAttack(player) {
            const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
            const config = global.summonConfig.skill373;
            summonmonsters(gplayerimp, config, config.duration);
        }
    }
    
    const skillHooks = new Map();

    function registerSkillHook(skillId, hook) {
        skillHooks.set(skillId, hook);
    }

    function setupSkillInterceptor() {
        const findSkillFunctionAddress = (skillId) => {
            const len = String(skillId).length + 9;
            const symbol = `_ZNK4GNET${len}Skill${skillId}Stub11StateAttackEPNS_5SkillE`;
            return Module.findExportByName(null, symbol);
        };

        for (const [skillId, hook] of skillHooks.entries()) {
            const address = findSkillFunctionAddress(skillId);
            Interceptor.replace(address, new NativeCallback((stub, skillPtr) => {
                const skill = new Skill(skillPtr);
                const playerPtr = skill.GetPlayer();
                const player = new PlayerWrapper(playerPtr);
                hook.StateAttack(player);
            }, 'void', ['pointer', 'pointer']));
        }
    }

    function activateAllSkills() {
        const skillList = [
            new skill370(),
            new skill371(),
            new skill372(),
            new skill373()
        ];

        for (const skill of skillList) {
            registerSkillHook(skill.skillId, skill);
        }

        setupSkillInterceptor();
    }

    loadConfig();
    activateAllSkills();

    })();
    