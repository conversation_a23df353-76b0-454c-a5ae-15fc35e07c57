(function(){
    // 内联包含CPlusClass以避免依赖问题
    class CPlusClass {
        constructor(pointer) {
            this.pointer = pointer;
        }
    
        ensurePointer() {
            if (!this.pointer || this.pointer.isNull()) {
                throw new Error("Invalid pointer");
            }
        }
        
        getPointer() {
            return this.pointer;
        }
    }
    
    // SkillState 类定义
    class SkillState {
        originalCalculate;
        originalStub;
        
        Calculate(player, skill, gplayerimp) {
            if (this.originalCalculate && this.originalStub) {
                this.originalCalculate(this.originalStub, skill.getPointer());
            }
        }
    }
    
    // SkillStateAttack 类定义
    class SkillStateAttack {
        originalStateAttack;
        originalStub;
        
        StateAttack(player, skill, gplayerimp) {
            if (this.originalStateAttack && this.originalStub) {
                this.originalStateAttack(this.originalStub, skill.getPointer());
            }
        }
    }
    
    // SkillBlessMe 类定义
    class SkillBlessMe {
        originalBlessMe;
        originalStub;
    
        BlessMe(player, skill, gplayerimp) {
            if (this.originalBlessMe && this.originalStub) {
                this.originalBlessMe(this.originalStub, skill.getPointer());
            }
        }
    }
    
    // 通用获取原生函数功能
    const functionMap = new Map();
    const getNativeFunction = (symbolOrAddress, ret, args) => {
        if (functionMap.has(symbolOrAddress)) {
            return functionMap.get(symbolOrAddress);
        }
    
        let address;
        try {
            if (symbolOrAddress.startsWith('0x')) {
                address = ptr(symbolOrAddress);
            } else {
                address = Module.findExportByName(null, symbolOrAddress);
                if (!address) {
                    return null;
                }
            }
            
            const f = new NativeFunction(address, ret, args);
            functionMap.set(symbolOrAddress, f);
            return f;
        } catch(e) {
            return null;
        }
    };
    
    // GPlayerImp 类实现
    class GPlayerImp extends CPlusClass {
        // 添加静态Map来存储玩家实例
        static allPlayerInstances = new Map();
    
        static fromPlayerWrapper(playerWrapper) {
            try {
                const playerPtr = playerWrapper.getPointer();
                const gplayerImpPtr = playerPtr.add(0x4).readPointer();
                if (!gplayerImpPtr.isNull()) {
                    return new GPlayerImp(gplayerImpPtr);
                }
                throw new Error("Could not get valid GPlayerImp pointer");
            } catch (error) {
                return null;
            }
        }
    
        creatureGenerator(creatureId, duration, count) {
            try {
                this.ensurePointer();
                
                const oi = Memory.alloc(0x08);
                oi.add(0).writePointer(this.pointer); // 直接使用GPlayerImp的指针
                oi.add(4).writeU32(0);
                const mp = Memory.alloc(0x48);
                mp.add(0).writeU32(creatureId);
                mp.add(0x08).writeU32(duration);
                mp.add(0x14).writeFloat(1.0);
                mp.add(0x18).writeFloat(1.0);
                mp.add(0x1c).writeFloat(1.0);
    
                const createMinorsFunc = new NativeFunction(
                    ptr("0x81c3b3c"), // object_interface::CreateMinors
                    "void",
                    ["pointer", "pointer", "float"]
                );
                
                for (let i = 0; i < count; i++) {
                    createMinorsFunc(oi, mp, 6.0);
                }
                return true;
            } catch (error) {
                return false;
            }
        }
    }
    
    // PlayerWrapper 类实现 - 极简版本，只提供必要的方法
    class PlayerWrapper extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }
        
        // 获取当前玩家的PlayerWrapper实例
        static getCurrentPlayer() {
            try {
                // 尝试获取当前玩家指针的地址
                // 根据实际情况，这可能需要调整
                const playerPtrAddress = ptr("0x091FE670"); // 这个地址需要根据实际情况调整
                const playerPtr = Memory.readPointer(playerPtrAddress);
                
                if (playerPtr && !playerPtr.isNull()) {
                    return new PlayerWrapper(playerPtr);
                }
                return null;
            } catch (error) {
                return null;
            }
        }
    }
    
    // Skill 类实现
    class Skill extends CPlusClass {
        constructor(pointer) {
            super(pointer);
        }
        
        // Level
        GetLevel() {
            return this.pointer.add(0x4).readInt();
        }
        
        // Player
        GetPlayer() {
            return this.pointer.add(0x61).readPointer();
        }
    }
    
    // SkillChangeInfo 类实现
    class SkillChangeInfo {
        constructor(type, value) {
            this.type = type;
            this.value = value;
        }
        
        static get TYPES() {
            return {
                VALUE: 'value',      // 直接设置值
                RATIO: 'ratio',      // 比率改变
                MULTIPLY: 'multiply' // 倍数改变
            };
        }
    }
    
    // SkillStubBase 类实现
    class SkillStubBase {
        constructor(skillId) {
            this.skillId = skillId;
        }
    }
    
    // SkillHook 类实现
    class SkillHook {
        skillId;
        calculateStates = [];  // 存储多段Calculate的配置
    
        constructor(skillId) {
            this.skillId = skillId;
        }
    
        // 为了保持API兼容性，提供一个getter
        get StateAttack() {
            // 返回一个函数，这个函数将作为StateAttack方法被调用
            return (player, skill) => {};
        }
    }
    
    // 配置文件读取功能
    function loadConfig() {
        try {
            // 配置文件路径 - 支持多种路径格式
            const configPaths = [
                "./summon_config_new.txt",       // 新配置文件
                "./summon_config.txt",           // 当前目录
                "summon_config.txt",             // 相对路径
                "./召唤符源码/summon_config.txt", // 源码目录
                "召唤符源码/summon_config.txt",   // 源码目录相对路径
                "/root/summon_config.txt",       // Linux路径
                "C:\\summon_config.txt",         // Windows根目录
                "summon_config.json"             // JSON格式
            ];
    
            // 默认配置 - 支持两种格式
            const defaultConfig = {
                // 格式1：简单格式（所有生物相同数量）
                // skill2008: { creatureIds: [22251, 22252, 22253], duration: 3600, count: 1 },
    
                // 格式2：高级格式（每个生物不同数量）
                skill2008: {
                    monsters: [
                        // { id: 7, count: 2 },  
                        // { id: 576, count: 3 },  
                        // { id: 577, count: 1 }   
                    ],
                    duration: 3600
                },
                skill2009: {
                    monsters: [
                        // { id: 7, count: 1 },  
                        // { id: 577, count: 2 }  
                    ],
                    duration: 3600
                },
                skill2010: {
                    monsters: [
                        // { id: 7, count: 1 } 
                    ],
                    duration: 3600
                },
                skill2385: {
                    monsters: [
                        // { id: 7, count: 1 } 
                    ],
                    duration: 3600
                },
            };
    
            let config = defaultConfig;
            let configLoaded = false;
            let lastError = null;
    
            // 尝试从多个路径读取配置文件
            for (const configFile of configPaths) {
                try {
                    // 尝试使用File API
                    let content = null;
                    try {
                        if (typeof File !== 'undefined') {
                            const file = new File(configFile, "r");
                            if (file) {
                                content = file.readText();
                                file.close();
                            }
                        }
                    } catch (fileError) {
    
                        // 尝试使用Node.js fs模块
                        try {
                            if (typeof require !== 'undefined') {
                                const fs = require('fs');
                                if (fs.existsSync(configFile)) {
                                    content = fs.readFileSync(configFile, 'utf8');
                                }
                            }
                        } catch (fsError) {
                            // 忽略
                        }
    
                        // 尝试使用Java API
                        try {
                            if (typeof Java !== 'undefined') {
                                const Files = Java.use("java.nio.file.Files");
                                const Paths = Java.use("java.nio.file.Paths");
                                const path = Paths.get(configFile);
                                if (Files.exists(path)) {
                                    const bytes = Files.readAllBytes(path);
                                    content = String.fromCharCode.apply(null, bytes);
                                }
                            }
                        } catch (javaError) {
                            // 忽略
                        }
                    }
    
                    if (content && content.trim()) {
                        try {
                            const userConfig = JSON.parse(content);
                            for (const key in userConfig) {
                                if (defaultConfig.hasOwnProperty(key)) {
                                    const skillConfig = userConfig[key];
                                    if (skillConfig) {
                                        if (Array.isArray(skillConfig.monsters)) {
                                            config[key] = {
                                                monsters: skillConfig.monsters,
                                                duration: typeof skillConfig.duration === 'number' ? skillConfig.duration : 3600
                                            };
                                        }
                                        else if (Array.isArray(skillConfig.creatureIds)) {
                                            config[key] = {
                                                creatureIds: skillConfig.creatureIds,
                                                duration: typeof skillConfig.duration === 'number' ? skillConfig.duration : 3600,
                                                count: typeof skillConfig.count === 'number' ? skillConfig.count : 1
                                            };
                                        }
                                    }
                                }
                            }
                            configLoaded = true;
                            break;
                        } catch (parseError) {
                            lastError = parseError;
                        }
                    }
                } catch (e) {
                    lastError = e;
                }
            }
    
            if (!configLoaded) {
                try {
                    const exampleConfig = JSON.stringify(defaultConfig, null, 2);
                    const examplePath = "./summon_config.txt.example";
                    const exampleFile = new File(examplePath, "w");
                    if (exampleFile) {
                        exampleFile.write(exampleConfig);
                        exampleFile.flush();
                        exampleFile.close();
                    }
                } catch (e) {
                    // 忽略
                }
            }
    
            global.summonConfig = config;
            return config;
        } catch (error) {
            const fallbackConfig = {
                skill2008: {
                    monsters: [
                        // { id: 7, count: 2 },
                        // { id: 576, count: 3 },
                        // { id: 577, count: 1 }
                    ],
                    duration: 3600
                },
                skill2009: {
                    monsters: [
                        // { id: 7, count: 1 },
                        // { id: 577, count: 2 }
                    ],
                    duration: 3600
                },
                skill2010: {
                    monsters: [
                        // { id: 7, count: 1 }
                    ],
                    duration: 3600
                },
                skill2385: {
                    monsters: [
                        // { id: 7, count: 1 }
                    ],
                    duration: 3600
                },
            };
            global.summonConfig = fallbackConfig;
            return fallbackConfig;
        }
    }
    
    // -------------------------------------------------------------------------
    // 召唤音咒实现部分
    // -------------------------------------------------------------------------
    
    // 通用函数：支持召唤单个或多个生物（兼容新旧配置格式）
    function summonmonsters(gplayerimp, config, duration = 3600) {
        try {
            if (!gplayerimp || gplayerimp.pointer.isNull()) {
                return false;
            }
    
            // 检查配置格式
            if (config.monsters && Array.isArray(config.monsters)) {
                for (const creature of config.monsters) {
                    if (typeof creature.id !== 'number' || creature.id <= 0) {
                        continue;
                    }
                    const count = typeof creature.count === 'number' ? creature.count : 1;
                    for (let i = 0; i < count; i++) {
                        gplayerimp.creatureGenerator(creature.id, duration, 1);
                    }
                }
            } else if (config.creatureIds && Array.isArray(config.creatureIds)) {
                const count = config.count || 1;
                for (const id of config.creatureIds) {
                    if (typeof id !== 'number' || id <= 0) {
                        continue;
                    }
                    for (let i = 0; i < count; i++) {
                        gplayerimp.creatureGenerator(id, duration, 1);
                    }
                }
            } else {
                return false;
            }
            return true;
        } catch (error) {
            return false;
        }
    }
    
    // 召唤音咒·背叛者
    class skill2008 extends SkillHook {
        constructor() {
            super(2008);
        }
    
        StateAttack(player, skill) {
            try {
                if (!player || player.pointer.isNull()) {
                    return;
                }
    
                const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                if (!gplayerimp) {
                    return;
                }
    
                // 从配置中获取参数
                const config = global.summonConfig?.skill2008 || {
                    monsters: [
                        // { id: 7, count: 2 },
                        // { id: 577, count: 3 },
                        // { id: 577, count: 1 }
                    ],
                    duration: 3600
                };
    
                // 召唤背叛者生物
                summonmonsters(gplayerimp, config, config.duration);
            } catch (error) {
                // 忽略错误
            }
        }
    }
    
    // 召唤音咒·邪恶先锋
    class skill2009 extends SkillHook {
        constructor() {
            super(2009);
        }
    
        StateAttack(player, skill) {
            try {
                if (!player || player.pointer.isNull()) {
                    return;
                }
    
                const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                if (!gplayerimp) {
                    return;
                }
    
                // 从配置中获取参数
                const config = global.summonConfig?.skill2009 || {
                    monsters: [
                        // { id: 7, count: 1 },
                        // { id: 577, count: 2 }
                    ],
                    duration: 3600
                };
    
                // 召唤邪恶先锋生物
                summonmonsters(gplayerimp, config, config.duration);
            } catch (error) {
                // 忽略错误
            }
        }
    }
    
    // 召唤音咒·八荒火龙
    class skill2010 extends SkillHook {
        constructor() {
            super(2010);
        }
    
        StateAttack(player, skill) {
            try {
                if (!player || player.pointer.isNull()) {
                    return;
                }
    
                const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                if (!gplayerimp) {
                    return;
                }
    
                // 从配置中获取参数
                const config = global.summonConfig?.skill2010 || {
                    monsters: [
                        // { id: 7, count: 1 }
                    ],
                    duration: 3600
                };
    
                // 召唤八荒火龙生物
                summonmonsters(gplayerimp, config, config.duration);
            } catch (error) {
                // 忽略错误
            }
        }
    }
    
    // 召唤音咒·饕餮
    class skill2385 extends SkillHook {
        constructor() {
            super(2385);
        }
    
        StateAttack(player, skill) {
            try {
                if (!player || player.pointer.isNull()) {
                    return;
                }
    
                const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                if (!gplayerimp) {
                    return;
                }
    
                // 从配置中获取参数
                const config = global.summonConfig?.skill2385 || {
                    monsters: [
                        { id: 7, count: 1 }
                    ],
                    duration: 3600
                };
    
                // 召唤饕餮生物
                summonmonsters(gplayerimp, config, config.duration);
            } catch (error) {
                // 忽略错误
            }
        }
    }
    
    // 简化版本的技能钩子系统，只专注于StateAttack功能
    const skillHooks = new Map();
    
    // 注册技能钩子函数
    function registerSkillHook(skillId, hook) {
        skillHooks.set(skillId, hook);
    }
    
    // 简单的拦截器，用于监听技能使用
    function setupSkillInterceptor() {
        try {
            // 查找使用技能的拦截点
            // 根据SkillHook.js中的方式动态构建函数名称
            const findSkillFunctionAddress = (skillId) => {
                const len = String(skillId).length + 9;
                const symbol = `_ZNK4GNET${len}Skill${skillId}Stub11StateAttackEPNS_5SkillE`;
                return Module.findExportByName(null, symbol);
            };
            
            // 设置每个技能的StateAttack钩子
            for (const [skillId, hook] of skillHooks.entries()) {
                const address = findSkillFunctionAddress(skillId);
                if (address) {
                    // 原始函数保存起来
                    const originalStateAttack = new NativeFunction(address, 'void', ['pointer', 'pointer']);
                    
                    // 替换函数实现
                    Interceptor.replace(address, new NativeCallback((stub, skillPtr) => {
                        try {
                            if (!skillPtr.isNull()) {
                                const skill = new Skill(skillPtr);
                                const playerPtr = skill.GetPlayer();
                                if (playerPtr && !playerPtr.isNull()) {
                                    const player = new PlayerWrapper(playerPtr);
                                    const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                                    
                                    // 调用我们自定义的StateAttack
                                    hook.StateAttack(player, skill);
                                }
                            }
                        } catch (error) {
                            // 出错时尝试调用原始函数
                            try {
                                originalStateAttack(stub, skillPtr);
                            } catch (e) {
                                // 忽略原始函数调用错误
                            }
                        }
                    }, 'void', ['pointer', 'pointer']));
                }
            }
            
            return true;
        } catch (error) {
            return false;
        }
    }
    
    // 暴露全局API
    function setupGlobalApi() {
        try {
            // 创建全局召唤API对象
            global.summonApi = {
                summonmonsters: summonmonsters,
                // 重新加载配置文件
                reloadConfig: function() {
                    return loadConfig();
                },
                // 显示当前配置
                showConfig: function() {
                    return global.summonConfig;
                },
                // 测试文件读取功能
                testFileRead: function(filePath = "./summon_config_new.txt") {
                    try {
                        if (typeof File !== 'undefined') {
                            try {
                                const file = new File(filePath, "r");
                                if (file) {
                                    const content = file.readText();
                                    file.close();
                                    return content;
                                }
                            } catch (e) {
                                // 忽略
                            }
                        }
    
                        if (typeof require !== 'undefined') {
                            try {
                                const fs = require('fs');
                                if (fs.existsSync(filePath)) {
                                    const content = fs.readFileSync(filePath, 'utf8');
                                    return content;
                                }
                            } catch (e) {
                                // 忽略
                            }
                        }
    
                        return null;
                    } catch (error) {
                        return null;
                    }
                },
                // 按ID召唤各种生物
                summonBackstabber: function() {
                    const player = PlayerWrapper.getCurrentPlayer();
                    if (player) {
                        const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                        if (gplayerimp) {
                            return summonmonsters(gplayerimp, [22251, 22252, 22253]);
                        }
                    }
                    return false;
                },
                summonEvilVanguard: function() {
                    const player = PlayerWrapper.getCurrentPlayer();
                    if (player) {
                        const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                        if (gplayerimp) {
                            return summonmonsters(gplayerimp, [22106, 22107]);
                        }
                    }
                    return false;
                },
                summonDragon: function() {
                    const player = PlayerWrapper.getCurrentPlayer();
                    if (player) {
                        const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                        if (gplayerimp) {
                            return summonmonsters(gplayerimp, 13875);
                        }
                    }
                    return false;
                },
                summonTaotie: function() {
                    const player = PlayerWrapper.getCurrentPlayer();
                    if (player) {
                        const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                        if (gplayerimp) {
                            return summonmonsters(gplayerimp, 123850);
                        }
                    }
                    return false;
                },
                summonBeastGod: function() {
                    const player = PlayerWrapper.getCurrentPlayer();
                    if (player) {
                        const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                        if (gplayerimp) {
                            return summonmonsters(gplayerimp, 13581);
                        }
                    }
                    return false;
                },
                // 自定义召唤函数
                summonById: function(creatureId, duration = 3600, count = 1) {
                    const player = PlayerWrapper.getCurrentPlayer();
                    if (player) {
                        const gplayerimp = GPlayerImp.fromPlayerWrapper(player);
                        if (gplayerimp) {
                            return gplayerimp.creatureGenerator(creatureId, duration, count);
                        }
                    }
                    return false;
                }
            };
            return true;
        } catch (error) {
            return false;
        }
    }
    
    // 激活所有技能钩子
    function activateAllSkills() {
        try {
            // 创建技能实例
            const skillList = [
                new skill2008(),  // 背叛者
                new skill2009(),  // 邪恶先锋
                new skill2010(),  // 八荒火龙
                new skill2385(),  // 饕餮
    
            ];
            
            // 注册所有技能钩子
            for (const skill of skillList) {
                registerSkillHook(skill.skillId, skill);
            }
            
            // 设置技能拦截器
            setupSkillInterceptor();
            
            return true;
        } catch (error) {
            return false;
        }
    }
    
    // -------------------------------------------------------------------------
    // 聊天监控和过滤功能
    // -------------------------------------------------------------------------

    // 聊天过滤配置
    let chatConfig = {
        enabled: true,
        replacements: [
            '系统消息已过滤',
            '内容不当已屏蔽',
            '消息已被过滤'
        ]
    };

    // 简化的配置
    let bannedWords = [];
    let wordReplacements = new Map(); // 词汇替换映射

    // 简化的配置加载
    function loadChatConfig() {
        try {
            let content = null;

            // 尝试读取配置文件
            try {
                if (typeof File !== 'undefined') {
                    const file = new File("./chat_filter.txt", "r");
                    if (file) {
                        content = file.readText();
                        file.close();
                    }
                }
            } catch (e) {
                // 忽略文件读取错误
            }

            // 清空现有配置
            bannedWords = [];
            wordReplacements.clear();

            if (content && content.trim()) {
                const lines = content.split('\n');

                for (const line of lines) {
                    const trimmed = line.trim();
                    if (!trimmed || trimmed.startsWith('#')) {
                        continue;
                    }

                    // 检查是否是词汇替换（包含->符号）
                    if (trimmed.includes('->')) {
                        const parts = trimmed.split('->').map(p => p.trim());
                        if (parts.length === 2 && parts[0] && parts[1]) {
                            wordReplacements.set(parts[0], parts[1]);
                        }
                    } else {
                        // 否则作为违禁词
                        bannedWords.push(trimmed);
                    }
                }
            } else {
                // 创建默认配置文件
                const defaultContent = `# 聊天过滤配置文件
# 以#开头的行为注释
# 格式：违禁词 或 原词->替换词

# 违禁词（会被完全过滤）
外挂
代练
金币

# 词汇替换（不过滤，只替换）
草->艹
操->艹
666->厉害`;

                try {
                    const file = new File("./chat_filter.txt", "w");
                    if (file) {
                        file.write(defaultContent);
                        file.flush();
                        file.close();
                    }
                } catch (e) {
                    // 忽略文件创建错误
                }

                // 设置默认规则
                wordReplacements.set('草', '艹');
                wordReplacements.set('操', '艹');
                wordReplacements.set('666', '厉害');
            }

            if (!wordsLoaded) {
                // 创建默认配置文件
                const defaultContent = `# 聊天过滤配置文件 - 增强版
# 支持多种功能：违禁词过滤、词汇替换、自定义替换消息

[banned]
QQ
群号
联系
下载
网址
客服

[strict]
外挂
作弊
代练
金币
元宝
RMB

[replacements]
系统消息已过滤
内容不当已屏蔽
消息已被过滤

[word_replace]
草->艹
操->艹
666->厉害
牛逼->牛批`;

                try {
                    const file = new File("./chat_filter.txt", "w");
                    if (file) {
                        file.write(defaultContent);
                        file.flush();
                        file.close();
                    }
                } catch (e) {
                    // 忽略文件创建错误
                }

                // 手动设置一些默认的词汇替换规则
                wordReplacements.set('草', '艹');
                wordReplacements.set('操', '艹');
                wordReplacements.set('666', '厉害');
                wordReplacements.set('牛逼', '牛批');

                // 设置一些默认的自定义替换消息
                customReplacements.push('系统消息已过滤');
                customReplacements.push('内容不当已屏蔽');
                customReplacements.push('消息已被过滤');
            }

            global.chatConfig = chatConfig;
            global.bannedWords = bannedWords;
            global.wordReplacements = wordReplacements;

            return { chatConfig, bannedWords, wordReplacements };
        } catch (error) {
            return { chatConfig, bannedWords: [], wordReplacements: new Map() };
        }
    }

    // 简化的过滤规则
    const filterRules = {
        // 检查消息是否需要过滤
        shouldFilter(message) {
            if (!chatConfig.enabled) {
                return false;
            }

            // 检查是否包含违禁词
            for (const keyword of bannedWords) {
                if (message.includes(keyword)) {
                    return true;
                }
            }
            return false;
        },

        // 获取替换后的消息
        getReplacementMessage() {
            const randomIndex = Math.floor(Math.random() * chatConfig.replacements.length);
            return chatConfig.replacements[randomIndex];
        },

        // 应用词汇替换
        applyWordReplacements(message) {
            let result = message;
            // 直接遍历Map进行替换
            wordReplacements.forEach((replacement, original) => {
                // 使用split和join方式替换，避免正则表达式问题
                result = result.split(original).join(replacement);
            });
            return result;
        },

        // 检查消息是否需要词汇替换
        shouldReplaceWords(message) {
            for (const [original] of wordReplacements) {
                if (message.toLowerCase().includes(original.toLowerCase())) {
                    return true;
                }
            }
            return false;
        }
    };

    // 聊天Hook功能
    function hookChat() {
        if (!chatConfig.enabled) {
            return;
        }

        try {
            // 监听handle_user_chat函数
            Interceptor.attach(ptr('0x080640AA'), {
                onEnter(args) {
                    try {
                        this.args = args;  // 保存参数供后续使用
                        const player_id = args[2].toInt32();
                        const msg = args[3];
                        const msg_length = args[4].toInt32();
                        const channel = args[7].toInt32();

                        if (!msg || msg.isNull() || msg_length <= 0) {
                            return;
                        }

                        // 读取消息内容
                        const msgContent = msg.readUtf16String(msg_length / 2);

                        // 只处理有效的消息
                        if (msgContent && msgContent.length > 0) {
                            let finalMessage = msgContent;

                            // 先检查违禁词过滤
                            let isFiltered = false;
                            for (const keyword of bannedWords) {
                                if (msgContent.includes(keyword)) {
                                    finalMessage = "系统消息已过滤";
                                    isFiltered = true;
                                    break;
                                }
                            }

                            // 如果没有被过滤，进行词汇替换
                            if (!isFiltered) {
                                for (const [original, replacement] of wordReplacements) {
                                    finalMessage = finalMessage.split(original).join(replacement);
                                }
                            }

                            // 如果消息有变化，替换它
                            if (finalMessage !== msgContent) {
                                const newMsgLen = finalMessage.length * 2;
                                const newMsgBuf = Memory.alloc(newMsgLen + 2);
                                newMsgBuf.writeUtf16String(finalMessage);

                                args[3] = newMsgBuf;
                                args[4] = ptr(newMsgLen);
                                this.isModified = true;
                            }
                        }
                    } catch(e) {
                        if (!e.message.includes('access violation')) {
                            // 静默处理错误
                        }
                    }
                },

                onLeave(retval) {
                    // 简化清理
                    this.isModified = false;
                }
            });

            // 监听聊天消息发送函数
            Interceptor.attach(ptr('0x084FDB68'), {
                onEnter(args) {
                    if (!chatConfig.enabled) return;

                    try {
                        const msgStruct = args[0];
                        if (!msgStruct || msgStruct.isNull()) {
                            return;
                        }

                        // 获取消息内容
                        const msgBase = msgStruct.add(0x18).readPointer();
                        const msgSize = msgStruct.add(0x20).readU32();
                        const channel = msgStruct.add(0x0C).readU8();
                        const senderId = msgStruct.add(0x10).readInt();

                        if (!msgBase || msgBase.isNull() || msgSize <= 0) {
                            return;
                        }

                        // 读取消息内容（静默监控，不输出日志）
                        const msgContent = msgBase.readUtf16String(msgSize / 2);
                    } catch(e) {
                        if (!e.message.includes('access violation')) {
                            // 静默处理错误
                        }
                    }
                }
            });

        } catch(e) {
            // 静默处理Hook安装失败
        }
    }

    // 简化的API
    function updateGlobalApiWithChat() {
        try {
            if (global.summonApi) {
                global.summonApi.chatFilter = {
                    // 重新加载配置
                    reloadConfig: function() {
                        return loadChatConfig();
                    },
                    // 显示当前配置
                    showConfig: function() {
                        return {
                            enabled: chatConfig.enabled,
                            bannedWords: bannedWords,
                            wordReplacements: Object.fromEntries(wordReplacements)
                        };
                    },
                    // 启用/禁用过滤
                    setEnabled: function(enabled) {
                        chatConfig.enabled = !!enabled;
                        return chatConfig.enabled;
                    },
                    // 添加违禁词
                    addBannedWord: function(word) {
                        if (word && !bannedWords.includes(word)) {
                            bannedWords.push(word);
                            return true;
                        }
                        return false;
                    },
                    // 添加词汇替换
                    addWordReplacement: function(original, replacement) {
                        if (original && replacement) {
                            wordReplacements.set(original, replacement);
                            return true;
                        }
                        return false;
                    },
                    // 测试词汇替换
                    testWordReplacement: function(message) {
                        return filterRules.applyWordReplacements(message);
                    },
                    // 测试违禁词过滤
                    testFilter: function(message) {
                        return filterRules.shouldFilter(message);
                    },
                    // 获取词汇替换规则
                    getWordReplacements: function() {
                        return Object.fromEntries(wordReplacements);
                    },
                    // 保存配置
                    saveConfig: function() {
                        try {
                            let content = "# 聊天过滤配置文件\n";
                            content += "# 违禁词（会被完全过滤）\n";
                            for (const word of bannedWords) {
                                content += word + "\n";
                            }
                            content += "\n# 词汇替换（原词->替换词）\n";
                            for (const [original, replacement] of wordReplacements) {
                                content += original + "->" + replacement + "\n";
                            }

                            const file = new File("./chat_filter.txt", "w");
                            if (file) {
                                file.write(content);
                                file.flush();
                                file.close();
                                return true;
                            }
                            return false;
                        } catch (error) {
                            return false;
                        }
                    }
                };
            }
            return true;
        } catch (error) {
            return false;
        }
    }

    // 启动插件
    try {
        loadConfig();
        loadChatConfig();
        setupGlobalApi();
        updateGlobalApiWithChat();
        activateAllSkills();
        hookChat();

        Script.bindWeak(global, function() {
            if (global.summonApi) {
                delete global.summonApi;
            }
            if (global.summonConfig) {
                delete global.summonConfig;
            }
            if (global.chatConfig) {
                delete global.chatConfig;
            }
            if (global.bannedWords) {
                delete global.bannedWords;
            }
            if (global.wordReplacements) {
                delete global.wordReplacements;
            }
        });

    } catch (error) {
        // 静默处理错误
    }

    })();
    